from fastapi.responses import HTMLResponse
import uuid
import hashlib
from Crypto.Cipher import AES
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import datetime
from typing import List, Optional, Dict
import schedule
import requests
from fastapi import FastAPI, HTTPException, Depends, Request, Form, Body
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.testclient import TestClient
from fastapi.responses import RedirectResponse
from dotenv import load_dotenv
import random
import os

from .DTOs.resourceDTO import ResourceListingResponseDTO, ResourceCreationResponseDTO, ResourceNewDTO
from .DTOs.guestDTO import GuestUpdatesSignUpRequest

import logging

import json

import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime, timedelta
from .api_configs import adhoc_url

from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY


class DailyRotatingFileHandler(RotatingFileHandler):
    def __init__(self, filename, *args, **kwargs):
        self.filename = filename
        self.current_date = datetime.now().strftime('%Y-%m-%d')
        super().__init__(self.get_daily_filename(), *args, **kwargs)

    def get_daily_filename(self):
        log_directory = os.path.join("log", f"log_{self.current_date}")
        os.makedirs(log_directory, exist_ok=True)  # Create the subdirectory if it doesn't exist
        return os.path.join(log_directory, f"{self.filename}_{self.current_date}.log")

    def emit(self, record):
        current_date = datetime.now().strftime('%Y-%m-%d')
        if current_date != self.current_date:
            self.current_date = current_date
            self.baseFilename = self.get_daily_filename()
            self.stream = self._open()
        super().emit(record)


logger = logging.getLogger('daily_logger')
logger.setLevel(logging.INFO)

handler = DailyRotatingFileHandler('log', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)

logger.addHandler(handler)

loggers = {}

# Logger 1
logger1 = logging.getLogger('logger1')
logger1.setLevel(logging.INFO)
# handler1 = DailyRotatingFileHandler('signup', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter1 = logging.Formatter('%(asctime)s : %(levelname)s : %(message)s')
# handler1.setFormatter(formatter1)
# logger1.addHandler(handler1)
loggers['logger1'] = logger1

logger2 = logging.getLogger('logger2')
logger2.setLevel(logging.INFO)
handler2 = DailyRotatingFileHandler('appointment', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter2 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler2.setFormatter(formatter2)
logger2.addHandler(handler2)
loggers['logger2'] = logger2

logger3 = logging.getLogger('logger3')
logger3.setLevel(logging.INFO)
handler3 = DailyRotatingFileHandler('payment', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter3 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler3.setFormatter(formatter3)
logger3.addHandler(handler3)
loggers['logger3'] = logger3

logger4 = logging.getLogger('logger4')
logger4.setLevel(logging.INFO)
# handler4 = DailyRotatingFileHandler('prescription', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter4 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
# handler4.setFormatter(formatter4)
# logger4.addHandler(handler4)
loggers['logger4'] = logger4

logger5 = logging.getLogger('logger5')
logger5.setLevel(logging.ERROR)
# handler4 = DailyRotatingFileHandler('prescription', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter4 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
# handler4.setFormatter(formatter4)
# logger5.addHandler(handler4)
loggers['logger5'] = logger5

logger6 = logging.getLogger('logger6')
logger6.setLevel(logging.INFO)
handler6 = DailyRotatingFileHandler('notification', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter6 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler6.setFormatter(formatter6)
logger6.addHandler(handler6)
loggers['logger6'] = logger6

logger7 = logging.getLogger('logger7')
logger7.setLevel(logging.INFO)
handler7 = DailyRotatingFileHandler('in_app_notifications', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter7 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler7.setFormatter(formatter7)
logger7.addHandler(handler7)
loggers['logger7'] = logger7

logger8 = logging.getLogger('logger8')
logger8.setLevel(logging.INFO)
# handler8 = DailyRotatingFileHandler('admin', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter8 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
# handler8.setFormatter(formatter8)
# logger8.addHandler(handler8)
loggers['logger8'] = logger8

logger9 = logging.getLogger('logger9')
logger9.setLevel(logging.ERROR)
# handler9 = DailyRotatingFileHandler('admin', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter9 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
# handler9.setFormatter(formatter9)
# logger9.addHandler(handler9)
loggers['logger9'] = logger9

logger10 = logging.getLogger('logger10')
logger10.setLevel(logging.INFO)
handler10 = DailyRotatingFileHandler('notification1', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter10 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler10.setFormatter(formatter10)
logger10.addHandler(handler10)
loggers['logger10'] = logger10

logger11 = logging.getLogger('logger11')
logger11.setLevel(logging.INFO)
handler11 = DailyRotatingFileHandler('Schedular', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter11 = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler11.setFormatter(formatter11)
logger11.addHandler(handler11)
loggers['logger11'] = logger11

logger_invoice_status = logging.getLogger('logger_invoice_status')
logger_invoice_status.setLevel(logging.INFO)
handler_invoice_status = DailyRotatingFileHandler('invoice_status', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter_invoice_status = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler_invoice_status.setFormatter(formatter_invoice_status)
logger_invoice_status.addHandler(handler_invoice_status)
loggers['logger_invoice_status'] = logger_invoice_status

logger_manage_family = logging.getLogger('logger_manage_family')
logger_manage_family.setLevel(logging.INFO)
handler_manage_family = DailyRotatingFileHandler('logger_manage_family', maxBytes=10 * 1024 * 1024, backupCount=30)
formatter_manage_family = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
handler_manage_family.setFormatter(formatter_manage_family)
logger_manage_family.addHandler(handler_manage_family)
loggers['logger_manage_family'] = logger_manage_family

# debug_logger = logging.getLogger('debug_logger')
# debug_logger.setLevel(logging.INFO)
# handler_debug_logger = DailyRotatingFileHandler('debug_logger', maxBytes=10 * 1024 * 1024, backupCount=30)
# formatter_debug_logger = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
# handler_debug_logger.setFormatter(formatter_debug_logger)
# debug_logger.addHandler(handler_debug_logger)
# loggers['debug_logger'] = debug_logger

"""log_directory = "logs"
date1=datetime.datetime.now()
log_file = os.path.join(log_directory, "{}.log".format(date1))

# Create the log directory if it doesn't exist
os.makedirs(log_directory, exist_ok=True)
import logging
from logging.handlers import TimedRotatingFileHandler, RotatingFileHandler

# Configure logging
log_file = "logs/my_log.log"

# Set up the timed rotating file handler for daily rotation
daily_rotating_handler = TimedRotatingFileHandler(log_file, when="midnight", interval=1, backupCount=3)
daily_rotating_handler.suffix = "%Y-%m-%d"
daily_rotating_handler.extMatch = r"^\d{4}-\d{2}-\d{2}$"

# Set up the rotating file handler for size-based rotation within daily files
max_log_size = 5 * 1024 * 1024  # 5 MB
backup_count = 3  # Number of backup log files to keep
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

rotating_handler = RotatingFileHandler(log_file, maxBytes=max_log_size, backupCount=backup_count)
rotating_handler.setFormatter(log_formatter)

# Set the logging level
logging_level = logging.DEBUG
rotating_handler.setLevel(logging_level)
daily_rotating_handler.setLevel(logging_level)

# Get the root logger and add the handlers
logger = logging.getLogger(__name__)
logger.setLevel(logging_level)
logger.addHandler(daily_rotating_handler)
daily_rotating_handler.setFormatter(log_formatter)
daily_rotating_handler.rolloverAt = 0  # Ensure daily rotation at startup
logger.addHandler(rotating_handler)"""

from .aws_s3 import AWSS3Client
from .ayoo_utils import encrypt_password, calcualte_appointment_duration
from .api_configs import OTP_GENERATOR, DATABASE_URL, p_redirect_url, p_cancel_url, T_url, \
    ALLOWED_ORIGINS, SCHEDULAR, EXCLUDE_ID, FEATURE_ENABLE_VITALS, VIDEO_SDK_API_KEY, VIDEO_SDK_API_SECRET, \
    CCAVENUE_MERCHANT_ID, CCAVENUE_ACCESS_CODE, CCAVENUE_WORKING_KEY
# from .cc_avenue_gateway import PaymentGateway
from .chat_controller import ChatController, ConnectionManager
from .chat_models import SaveChatModel, GetChat, CloseChat, GetUsersAllChat, BroadcastMessage
from .database import init_db
from .mental_health_case_models import AddChiefComplain, GetMentalHealthCaseDetails, SleepAndAppetite, \
    DoctorNotes, SubstanceUseAdd, CaseElement, Assessment, MentalHealthMedication, TreatmentPlanAdd, FollowUp, \
    GetDoctorCases, CaseSummary, TherapyTreatmentPlan, ClientEducation, TreatmentPlanResponse, \
    TherapyRecommendationAdd, ReferCase, CareAssessment, CareAssessmentPatientFeedback, \
    CareAssessmentRemoveAttribute, AssessmentFormsSubmit, PatientMedicalHistory, ClinicalHistoryPermission, \
    GetClinicalHistory, TherapyTreatmentPlanComment, GetReportsList, UploadExtraDocument, \
    DeleteReport, GetPatientReports, LabTest, GetReferralDoctorsList, TherapyNotes, DeleteChiefComplain, \
    ReferCaseUpdate, ReferCaseDelete, UploadPrescribedReport, DeleteDocument, UpdatePrescribedReport, \
    UpdateExtraDocument, CaseElement1, RelativePrescriptions, WorkUpPlan, WorkUpPlanAdd, GetDoctorCaseInfo

from .aws_msg_email_generator import AWSEmailAndMsgSender

from .mental_health_controller import PsychiatryController, TherapyController, MedicalHealthController, \
    MedicalHistoryController
from .metadata_controller import MetadataController
from .metadata_models import Specialization, SpecializationUpdate, PracticeArea, PracticeAreaUpdate, \
    SpecializationGet, PracticeAreaGet, FormAdd, FormUpdate, FormDelete, QuestionDelete, QuestionUpdate, \
    GetFormQuestions, QuestionAdd, QuestionResponse, SpecializationDelete, PracticeAreaDelete, QuestionResponseRangeAdd, \
    QuestionResponseRangeUpdate, QuestionResponseDelete, DropdownMetadata, MetadataListName, MetadataListNameUpdate, \
    DropdownMetadata, \
    DropdownMetadataUpdate, DropdownMetadataDelete, DropdownMetadataSearch, AddMedicineToCatalogue, \
    UpdateMedicineInCatalogue, DeleteMedicineFromCatalogue, GetMedicineBrandFromSalt, \
    GetMedicineDoseAndDrugFormFromBrandAndSalt, SearchICDCodeByBlock, GetMedicineSaltFromBrand

from .mongodb import mongodb_conn
from .dbmodels import DBUser, DBAdmin, DBDoctor, OTPStore, DBClinic, DBBenefits
from .doctor_controller import DoctorController
from .patient_controller import PatientController
from .doctormodels import DoctorSignUpView, DoctorSignupResponse, DoctorLoginView, DoctorLoginToken, \
    DoctorVerifyOtpView, DoctorChangePasswordView, DoctorAndClinicMapping, RequestAvailableSlots, \
    RequestDoctorsVirtualSlots, SearchDoctorsBasedOnSpecialization, UploadDoctorProfileImagesByAdmin, \
    VirtualAppointment, \
    VirtualAppointmentBooking, RequestAppointmentList, ResponseAppointmentList, RequestDoctorsVirtualAvailableSlots, \
    ResponseDoctorsVirtualAvailableSlots, UpdateDoctorSignUpView, GetDoctorById, DeleteDoctorResponseView, \
    VirtualAppointmentCopy, UploadDoctorSignatureImages, \
    UpdateDoctorAndClinicMapping, UploadDoctorProfileImages, SearchDoctorsBasedOnSpecialization, \
    UploadDoctorProfileImagesByAdmin, UpdateDoctorAndClinicMapping, UploadDoctorProfileImages, DoctorProfileImageView, \
    VirtualAppointmentCopy, ExtendEndDateOfCaseId, CloseActiveCaseId, ResponseCaseIdChange, FamilyDoctorStatus, \
    FamilyDoctorStatusAdmin, SearchByAreaOrSpecialization, SearchPatientsByName, SearchSlotsOnADate, \
    ActivateDoctorAccount
from .prescription_controller import PrescriptionController
from .jwt import create_access_token, create_access_token_new, get_payload_from_token, get_user_id_from_token, \
    oauth2_scheme, \
    get_valid_user_from_token, oauth2_scheme_admin, \
    get_valid_admin_from_token, oauth2_scheme_doctor, get_valid_doctor_from_token, oauth2_scheme_new, \
    check_valid_user_from_token, get_valid_admin_or_doctor_token, create_video_sdk_token, create_video_service_token
from .guest_controller import GuestController
from .view_controller import UserController, AdminController
from .viewmodels import UserSignUpView, SignupResponse, VerifyOtpView, UserLoginView, Token, AdminToken, \
    ClinicView, ClinicViewResponse, UserVerifyOtpResponseView, UserExistView, DeleteClinicView, UpdateClinicView, \
    AdminSignUpResponse, UpdateUserSignUpView, UserCreateView, GetUserDetailViewRequest, AdminSignUpView, \
    GetAdminDetailsView, GetAdminDetailsViewStatus, UserDetailsView, UserExtraInfo, \
    ActivateUserAccount, UserUpdateBasicDetails, CreateUserByAdmin, AddAdmin, AddFamilyMember, FamilyMemberSignUpView, \
    GenerateCaretakerAccessCodeView, GetCaretakerAccessByCodeView, ApproveFamilyMemberAddition, UpdateCaretakerAccess, \
    DeleteFamilyRelation, GetActiveCases, UpdateFamilyMember, DeleteUser, DeleteUserByAdmin
from .patient_models import AddMemberToFamilyView, AddPolicyPayloadResponse, AddRelativeToFamilyView, \
    AdminAddRelativeToFamilyView, AdminFamilyCodeGenRequest, AdminGetUserFamilyRequest, AdminRemoveFamilyMemberRequest, \
    AppointmentBooking, AppointmentBookingByAdmin, UpdateAppointmentStatus, appointmentstatus, appointmentstatusdel, \
    appointmentreason, appointmentreasondel, \
    AppointmentBookingDetailsCopy, AppointmentBookingDetailsCopy1, AppointmentBookingForOthers, \
    AppointmentBookingResponse, AppointmentById, CheckIFAccessRequest, CreateNewFamilyView, DataAccessView, \
    DeleteAppointmentResponseView, DeleteVirtualSlotsResponse, DeleteVirtualSlotsView, FamilyCodeGen, \
    GetAppointmentView, GetRelativeToken, GetRelativeTokenResponse, \
    GuestAppointmentBooking, GuestAppointmentBookingForOthers, PayloadDict, RemoveFamilyMemberRequest, \
    RequestGuestAppointmentList, \
    ResponseGuestAppointmentList, SearchClinicView, SearchClinicResponse, AddSymptomsImageView, \
    AddSymptomsImageResponse, SlotBlockingResponse, SlotBlockingView, UpdateSymptomsView, ResponseAppointmentsList, \
    GetSymptomsResponse, PatientHealthHistoryView, GetPatientHealthHistoryView, UploadReportRequestView, \
    ReportView, GetPatientReportView, GetPatientReportResponseView, UpdateReportRequestView, UpdateReportResponseView, \
    DeleteReportRequestView, DeleteReportResponseView, PatientFeedbackForDoctor, PatientFeedbackForDoctorResponse, \
    GetDoctorFeedback, GetDoctorFeedbackResponseView, SearchAllClinicView, SearchClinicIdView, SearchClinicIdResponse, \
    CheckSlotStatus, GetSymptomsById, DeleteSymptomsImageResponse, GetAppointmentById, AppointmentBookingDetails, \
    CheckDoctorAvailableSlot, CheckMappingForClinic, JitsiMeetingInfo, CheckJitsiMeetingCode, \
    CheckJitsiMeetingAppointmentId, PatientVitalsInfo, PatientVitalsInfoUserId, UploadPrescriptionRequestView, \
    UpdatePrescriptionRequestView, DeletePrescriptionRequestView, PrescriptionView, GetPatientPrescription, \
    PatientVitalsInfoDate, PatientVitalsInfoWithBMI, PatientVitalsInfoDateReadingId, PatientHealthHistoryViewUser, \
    AddFamilyResponse, AddFamilyView, PatientProfileImageView, UploadPatientProfileImages, \
    UploadPatientProfileImagesByAdmin, GetUserById, FamilyDoctorAdd, FamilyDoctorAddView, FamilyDoctorAddByAdmin, \
    DoctorAppointmentBooking, DoctorAppointmentBookingResponse, DoctorClinicAvailableSlot, \
    MentalHealthSelfAppointmentBooking, MentalHealthAppointmentBookingResponse, MentalHealthOthersAppointmentBooking, \
    MentalHealthGroupAppointmentBooking, PatientId, AppDownloadLink, PatientsRelatives, AppointmentCancellationModel, \
    RequestPrescriptionList, ConsentRecord, LogoutRequest, UIUsersView, PatientProfileMerge
from .membership_controller import MembershipController
from .membership_models import Benefits, CreateBenefitResponse, GetAllBenefitResponse, CreateAyooPlan, \
    GetAllMembershipPlans, UpdateMembershipPlan, CreateSubscriptionRequest, CreateSubscriptionResponse, \
    FindSubscription, FindSubscriptionResponse, FindIfMember, ResponseFindIfMember, FindIfMemberByMobile, \
    UserSubscriptionPayment, CreateSubscription, GetBenefitById, GetMembershipPlanByIdResponse, GetMembershipPlanById, \
    DeleteBenefitResponse, DeleteMembershipPlanResponse, AddMemberRequest, AddMemberResponse, AddMemberRequestByUser
from .prescription_model import AddPrescriptionRequest, AddPrescriptionResponse, \
    AddPrescription, AddPrescriptionResponseCopy, AdminAddPrescriptionRequest, DeletePrescriptionRequest, \
    FindPrescriptionCaseidRequest, FindPrescriptionsRequest
from .insert_data_ import InsertRandomData
from .jitsi_meet import JitsiMeetController
from .tasks import repeat_every
import logging
from fastapi.middleware.cors import CORSMiddleware
from .supporting_staff_models import SupportingStaffSignUpView, SupportingStaffSignUpInsert, \
    SupportingStaffUpdateView, SupportingStaffGetView, SupportingStaffAddToClinic
from .supporting_staff_controller import SupportingStaffController
from .relatives_controller import RelativesController
from .clinic_team_models import ClinicTeamView, ClinicTeamGetView, ClinicTeamAdd
from .clinic_team_controller import ClinicTeamController
from .firebase_models import UserDeviceInfoAdd, DeviceInfoAdd, UserDeviceInfoView, UserActiveDeviceView, \
    UserActiveDeviceGet, BroadCastMsg
from .firebase_controller import FireBaseDeviceController
from .doctor_team_models import DoctorTeamMembers, TeamMemberAddToDoctor, DoctorTeamGet, \
    DoctorTeamGetMember, \
    TeamMemberInfo
from .doctor_team_controller import DoctorTeamController

from .cron import CronController
from .scheduled_tasks import ScheduledTasksController

from .faq_controller import faqcontroller
from .faq_models import faqview, faqInfo, faqview1

from .revenue_controller import Revenuecontroller

from .revenue_models import Payment, Paymentview, Payments_appointment, Payment_info, Paymentstatus, \
    payment_payload, appointment_failed, One_Time_Payment, promo_code, promotion, promotion_admin, Adhoc_Payment, \
    Adhocview, Get_payment, Paymentsuccess, OrderQueryRequest
from .blogs_controller import BlogsController
from .blogs_models import BlogIdRequest

# FileUpload APIs
from .file_upload_service.file_upload_models import ALLOWED_FILE_TYPES, MAX_FILE_SIZE_BYTES, \
    CreateUploadRequest, FileUpload, GetUploadRequest, GetUserFiles, deleteUserFile, \
    deleteUploadRequest, ShareFileReq, FileUploadRequest, updateUploadRequest, supportedUploadRequest, GetActiveCases
from .file_upload_service.file_upload_controller import fileUploadContoller
from .file_upload_service.file_tags_models import CreateFileTag, GetFileTag, UpdateFileTag
from .file_upload_service.file_tags_controller import FileTagContoller
from .file_upload_service.request_forms_models import CreateRequestForms, getRequestForm, \
    updateRequestForm
from .file_upload_service.request_forms_controller import RequestFormContoller

import threading
import time
import schedule

from fastapi import WebSocket, WebSocketDisconnect
from fastapi import File, UploadFile, Query
from .admin import router
from .admin.views import admin_router
from .appointment_views import appointments_router
from .meet_views import meet_router
from .user_views import users_router
from .case_sheets.router import case_router
from .slot_views import slots_router
from .drug_search_views import drug_search_router
from .patient.views import patient_router
from .payment.views import payment_router
from .sms_templates.sms_templates_routes import router as sms_templates_router
from .text_local_service.text_local_routes import router as text_local_router
from .fire_base_notifications_v1.firebase_notifications_router_v1 import router as fcm_msg_router
from .vitals.vitals_routes import router as vitals_router
from .DAOs.appointmentDAO import AppointmentDAO, user_appointment_aggregation, \
    admin_dashboard_past_week_appointments_by_day
from .DAOs.allPatientsDAO import UsersWithAppointments, AllPatientsDAO, LastSevenDaysRegistered
from .payment.payment_controller import get_adhoc_mechant_json, get_payment_DAO

app = FastAPI()
app.include_router(router.router)

# Disable automatic generation of Open API UI documentation
# app.openapi_url = ""

app.include_router(admin_router)
app.include_router(appointments_router)
app.include_router(case_router)
app.include_router(sms_templates_router)
app.include_router(text_local_router)
app.include_router(slots_router)
app.include_router(meet_router)
app.include_router(drug_search_router)
app.include_router(patient_router)
app.include_router(payment_router)
app.include_router(users_router)
app.include_router(vitals_router)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    errors = []
    detail_key_errors = ''
    for error in exc.errors():
        errors.append({
            "loc": error["loc"],
            "msg": error["msg"],
            "type": error["type"]
        })
        if len(error['loc']) == 2:
            detail_key_errors += error['loc'][1] + ': ' + error.get('msg', '') + '. '
        else:
            detail_key_errors += error.get('msg', '') + '. '
    return JSONResponse(
        status_code=422,
        content={"detail": detail_key_errors, "errors": errors}
    )


current_env = os.getenv("CURRENT_ENV")
if current_env in ["uat", "dev"]:
    app.include_router(fcm_msg_router)

templates = Jinja2Templates(directory="ayoo_backend/api/templates")
app.mount("/static", StaticFiles(directory="ayoo_backend/api/static"), name="static")
# Mount .well-known folder
app.mount("/.well-known", StaticFiles(directory="ayoo_backend/api/static/.well-known"), name="well-known")

"""ALLOWED_ORIGINS = [
    "https://ayoo.care",
    "https://dev.ayoo.care",
    "https://uat.ayoo.care",
    "https://dev.api.ayoo.care",
    "https://uat.api.ayoo.care",
    "https://api.ayoo.care",
    "http://localhost:3000",
    "https://www.ayoo.care"
]"""

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

db1 = init_db(DATABASE_URL)
mongo = None
guest_controller = GuestController(db=db1, mongo=mongodb_conn)
ctrl = UserController(db=db1, otp_generator=OTP_GENERATOR, mongo=mongodb_conn)
admin_ctrl = AdminController(db=db1, mongo=mongodb_conn)
doctor_ctrl = DoctorController(db=db1, mongo=mongodb_conn)
ptn_ctrl = PatientController(db=db1, mongo=mongodb_conn)
prscpt_ctrl = PrescriptionController(db=db1, mongo=mongodb_conn)
membr_ctrl = MembershipController(db=db1, mongo=mongodb_conn)
chat_ctrl = ChatController(db=db1, mongo=mongodb_conn)
client_request = TestClient(app)
insert_random_data_ctrl = InsertRandomData(db=db1, mongo=mongodb_conn)
jitsi_ctrl = JitsiMeetController(db=db1, mongo=mongodb_conn)
sup_stf_ctrl = SupportingStaffController(db=db1, mongo=mongodb_conn)
relative_ctrl = RelativesController(db=db1, mongo=mongodb_conn)
clinic_team_ctrl = ClinicTeamController(db=db1, mongo=mongodb_conn)
doctor_team_ctrl = DoctorTeamController(db=db1, mongo=mongodb_conn)
firebase_ctrl = FireBaseDeviceController(db=db1, mongo=mongodb_conn)
cron_ctrl = CronController(db=db1, mongo=mongodb_conn)
schedule_ctrl = ScheduledTasksController(db=db1, mongo=mongodb_conn)
metadata_ctrl = MetadataController(db=db1, mongo=mongodb_conn)
psychiatry_ctrl = PsychiatryController(db=db1, mongo=mongodb_conn)
therapy_ctrl = TherapyController(db=db1, mongo=mongodb_conn)
medical_health_ctrl = MedicalHealthController(db=db1, mongo=mongodb_conn)
medical_history_ctrl = MedicalHistoryController(db=db1, mongo=mongodb_conn)
chat_manager = ConnectionManager(db=db1, mongo=mongodb_conn)
faq_cltr = faqcontroller(db=db1, mongo=mongodb_conn)
rev_cltr = Revenuecontroller(db=db1, mongo=mongodb_conn)
aws_mail_ctrl = AWSEmailAndMsgSender()
blog_ctrl = BlogsController(mongo=mongodb_conn)

# filleUpload Controller
file_upload_ctrl = fileUploadContoller(db=db1, mongo=mongodb_conn)
file_tag_ctrl = FileTagContoller(mongo=mongodb_conn)
request_form_ctrl = RequestFormContoller(mongo=mongodb_conn)


# payment_ctrl = PaymentGateway()


def run_continuously(interval=1):
    """Continuously run, while executing pending jobs at each
    elapsed time interval.
    @return cease_continuous_run: threading. Event which can
    be set to cease continuous run. Please note that it is
    *intended behavior that run_continuously() does not run
    missed jobs*. For example, if you've registered a job that
    should run every minute and you set a continuous run
    interval of one hour then your job won't be run 60 times
    at each interval but only once.
    """
    cease_continuous_run = threading.Event()

    class ScheduleThread(threading.Thread):
        @classmethod
        def run(cls):
            while not cease_continuous_run.is_set():
                schedule.run_pending()
                time.sleep(interval)

    continuous_thread = ScheduleThread()
    # continuous_thread.daemon = True  # Allows program to exit 
    continuous_thread.start()
    return cease_continuous_run


if SCHEDULAR == "True":
    print("schedular running")
    # schedule.every().day.at("00:00").do(schedule_ctrl.membership_expiry)
    # schedule.every().day.at("10:00").do(schedule_ctrl.membership_expiry_reminder)

    # schedule.every().day.at("20:00").do(schedule_ctrl.meds_reminder)

    # schedule.every().day.at("09:00").do(schedule_ctrl.ayoo_check_in_days)
    schedule.every(5).minutes.do(schedule_ctrl.Invoice_status1)
    schedule.every(5).minutes.do(schedule_ctrl.invoice_status_new)
    schedule.every(5).minutes.do(schedule_ctrl.invoice_status_check_for_unreleased_slots)
    # schedule.every(10).seconds.do(schedule_ctrl.Invoice_status1)
    schedule.every(5).minutes.do(schedule_ctrl.payment_status_failed)
    # schedule.every(5).minutes.do(schedule_ctrl.payment_status_check)
    # schedule.every(30).minutes.do(schedule_ctrl.adhoc_payment_status_check)

    schedule.every().day.at("00:00").do(schedule_ctrl.auto_submit_case_sheet)
    # schedule.every(5).seconds.do(schedule_ctrl.auto_submit_case_sheet)

    # Start the background thread
    stop_run_continuously = run_continuously()

    # Do some other things...
    time.sleep(1)
    #
    # # Stop the background thread
    # stop_run_continuously.set()

redirect_url_fetch = p_redirect_url
cancel_url_fetch = p_cancel_url

session = {}
languages = ['Assamese', 'Bengali', 'English', 'Gujarati', 'Hindi', 'Kannada', 'Maithili', 'Malayalam', 'Marathi',
             'Odia', 'Punjabi', 'Sanskrit', 'Tamil', 'Telugu', 'Urdu']
doctor_type = ['General', 'Specialists', 'Allied health professionals']
doctor_specialization = ['Audiologist', 'Cardiologist', 'Dentist', 'ENT Specialist', 'Endocrinologist', 'Gynaecologist',
                         'Neurologist', 'Oncologist', 'Orthopaedic', 'Paediatrician', 'Physician', 'Psychiatrists',
                         'Pulmonologist', 'Radiologist', 'Veterinarian']
doctor_allied = ['Dietitians', 'Physiotherapists',
                 'Podiatrists', 'Psychologists', 'Speech Pathologists']


@app.on_event("startup")
async def startup_event():
    # remove this after one time execution on production
    relative_ctrl.create_db_record_for_patient_ayoo_id()
    if SCHEDULAR == "True":
        cron_ctrl.schedule_next_run()
        # admin_ctrl.insert_admin_roles_on_startup()
        logger.info('app start.')


@app.get('/')
async def hello():
    return {"msg": "Hello Ayoo"}


@app.post("/user/token", response_model=Token, tags=["user"])
async def login_for_access_token(login_view: UserLoginView):
    loggers['logger1'].info("/user/token : Request : " + str(dict(mobile_or_email=login_view.mobile_or_email)))
    resp, msg = ctrl.login(login_viewmodel=login_view)
    if not resp:
        loggers['logger1'].info("/user/token : {status_code}, error: {detail}".format(status_code=401, detail=msg))
        raise HTTPException(status_code=410, detail=msg)  # need to revisit status_code
    logger.info("inside post req")
    # r1: DBUser = resp
    access_token = create_access_token(userid=resp['user_id'])
    loggers['logger1'].info("/user/token : Response : " + str(
        dict(Token(userid=resp['user_id'], access_token=access_token, token_type='bearer'))))
    loggers['logger1'].info("/user/token : status_code=200")
    return Token(userid=resp['user_id'], access_token=access_token, token_type='bearer')


@app.post("/user/logout", tags=["user"])
async def user_logout(
        body: LogoutRequest = Body(None),
        token: str = Depends(oauth2_scheme)
):
    loggers['logger1'].info("/user/logout : Requested")
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(userid)
    if user_check is not None:
        resp, error = ptn_ctrl.logout(userid, body)
        if resp is not None:
            loggers['logger1'].info("/user/logout : status_code=200")
            return resp
        else:
            loggers['logger1'].info("/user/logout failed with error:" + error)
            return error
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get("/user/me", response_model=UserDetailsView, tags=["user"])
async def return_me(token: str = Depends(oauth2_scheme)):
    logger.info("/user/me")
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    loggers['logger1'].info("/user/me : request :{ }" + "by " + str(userid))
    user_details = ctrl.get_user_details(userid=userid, mongo=mongodb_conn)
    if user_details is None:
        loggers['logger1'].info("/user/user/me : {status_code=409, detail= User not found}")
        raise HTTPException(status_code=409, detail='User not found')
    loggers['logger1'].info("/user/me : status_code=200")
    return UserDetailsView(userid=user_details['userid'],
                           ayoo_id=user_details.get('ayoo_id', ''),
                           firstname=user_details['firstname'],
                           lastname=user_details['lastname'],
                           dob=str(user_details['birthdate']),
                           email=user_details['email'],
                           mobile=user_details['mobile'],
                           gender=str(user_details['gender']),
                           ayoo_member_id=user_details['ayoo_member_id'],
                           profile_image_url=user_details['profile_image_url'],
                           family_doctor=user_details['family_doctor'],
                           marital_status=user_details['marital_status'],
                           address=user_details['address'],
                           city=user_details['city'],
                           locality=user_details['locality'],
                           pin=user_details['pin'],
                           state=user_details['state'],
                           country=user_details['country'],
                           personal_doctor_name=user_details['personal_doctor_name'],
                           personal_doctor_phone=user_details['personal_doctor_phone'],
                           personal_doctor_email=user_details['personal_doctor_email'],
                           emergency_contacts=user_details.get('emergency_contacts'),
                           new_user=user_details['new_user']
                           )


@app.post('/user/update_me', response_model=UserDetailsView, tags=["user"])
async def update_me(user: UserUpdateBasicDetails, token: str = Depends(oauth2_scheme)):
    logger.info("/user/update_me")
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    loggers['logger1'].info("/user/update_me : request :" + str(dict(user)) + "by " + str(userid))
    resp, msg = ctrl.update_user(userid=userid, user_data=user)
    if not resp:
        loggers['logger1'].info("/user/update_me : {status_code=409," + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:

        loggers['logger1'].info("/user/update_me : status_code=200")
        return UserDetailsView(userid=resp['userid'],
                               firstname=resp['firstname'],
                               lastname=resp['lastname'],
                               dob=str(resp['birthdate']),
                               email=resp['email'],
                               mobile=resp['mobile'],
                               gender=str(resp['gender']),
                               ayoo_member_id=resp['ayoo_member_id'],
                               profile_image_url=resp['profile_image_url'],
                               family_doctor=resp['family_doctor'],
                               marital_status=resp['marital_status'],
                               address=resp['address'],
                               city=resp['city'],
                               locality=resp['locality'],
                               pin=resp['pin'],
                               state=resp['state'],
                               country=resp['country'],
                               personal_doctor_name=resp['personal_doctor_name'],
                               personal_doctor_phone=resp['personal_doctor_phone'],
                               personal_doctor_email=resp['personal_doctor_email'],
                               emergency_contacts=resp.get('emergency_contacts')
                               )


@app.post('/user/signup', response_model=SignupResponse, tags=["user"])
async def signup(user: UserSignUpView):
    loggers['logger1'].info(
        "/user/signup : request : " + str(dict(firstname=user.firstname, lastname=user.lastname, dob=user.dob,
                                               email=user.email, mobile=user.mobile, gender=user.gender, password='')))
    # validating phone number
    if not re.match(r'^(?:\+91\d{10}|\d{10})$', user.mobile):
        raise HTTPException(status_code=400, detail='Invalid mobile number')
    resp, msg = ctrl.signup(signup_viewmodel=user)
    if not resp:
        loggers['logger1'].info("/user/signup : {status_code=409, detail=" + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger1'].info(
            "/user/signup : response: " + str(SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)))
        loggers['logger1'].info("/user/signup : status_code=200")
        return SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)


@app.post('/user/resend_otp', response_model=SignupResponse, tags=["user"])
async def resend_otp(otp: VerifyOtpView):
    loggers['logger1'].info("/user/resend_otp : request : " + str(dict(otp)))
    logger.info("/user/resend_otp")
    resp, msg = ctrl.resend_otp(otp_viewmodel=otp)
    if not resp:
        loggers['logger1'].info("/user/resend_otp : {status_code=409, detail=" + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger1'].info(
            "/user/resend_otp : request : " + str(dict(transaction_id=resp.transactionid, mobile=resp.mobile)))
        loggers['logger1'].info("/user/resend_otp : status_code=200")
        return SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)


@app.post('/user/verify_otp', tags=["user"])
async def verify_otp(otp: VerifyOtpView):
    loggers['logger1'].info("/user/verify_otp : request :" + str(dict(otp)))
    resp, msg = ctrl.verify_otp(otp_viewmodel=otp)
    if not resp:
        loggers['logger1'].info("/user/verify_otp : {status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:
        r1: DBUser = resp
        if msg.startswith("Added user "):
            send_welcome_msg, msg_resp = chat_ctrl.welcome_msg_to_new_user(userid=r1.userid)

        resp_relation_update, msg_relation_update = ctrl.update_is_register_in_relation_data(r1.userid)
        access_token = create_access_token(userid=r1.userid)
        loggers['logger1'].info("/user/verify_otp : Response : " + str(
            dict(userid=r1.userid, firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.birthdate),
                 email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender), profile_image_url=None,
                 access_token=access_token, token_type='bearer')))
        loggers['logger1'].info("/user/verify_otp : status_code=200")
        return dict(userid=r1.userid, firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.birthdate),
                    email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender), profile_image_url=None,
                    access_token=access_token, token_type='bearer')


@app.post('/user/login', tags=["user"])
async def login(login_view: UserLoginView):
    logger.info("/user/login")
    resp, msg = ctrl.login(login_viewmodel=login_view)
    if not resp:
        raise HTTPException(status_code=401, detail=msg)
    else:
        # r1: DBUser = resp
        access_token = create_access_token(userid=resp['user_id'], role='user')
        logger.info(access_token)
        # return dict(firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.birthdate), url=str(r1.url),
        #             email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender))
        return dict(firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['birthdate']),
                    url=resp['url'], email=resp['email'], mobile=resp['mobile'], password=resp['password'],
                    gender=str(resp['gender']), new_user=resp['new_user'])


@app.post('/user/delete', response_model=UserSignUpView, tags=["user"])
async def delete_user(delete_data: DeleteUser, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    logger.info("/user/delete")
    loggers['logger1'].info(f"/user/delete : request :{user_id}")
    resp, msg = ctrl.verify_and_delete_user(delete_data=delete_data, user_id=user_id)
    if not resp:
        loggers['logger1'].info("/user/delete : {status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=401, detail=msg)
    else:
        r1: DBUser = resp
        loggers['logger1'].info(
            "/user/delete : Response : " + str(dict(firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.birthdate),
                                                    email=r1.email, mobile=r1.mobile, password='',
                                                    gender=str(r1.gender))))
        loggers['logger1'].info("/user/delete : status_code=200")
        return UserSignUpView(firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.birthdate),
                              email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender))


@app.post('/user/reset_password', response_model=SignupResponse, tags=["user"])
async def reset_password(login_view: UserLoginView):
    logger.info("/user/reset_password")
    loggers['logger1'].info("/user/reset_password : request :" + str(dict(mobile_or_email=login_view.mobile_or_email)))
    resp, msg = ctrl.forgot_password(userlogin_viewmodel=login_view)
    if not resp:
        loggers['logger1'].info("/user/reset_password : {status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=401, detail=msg)
    else:
        r1: DBUser = resp
        loggers['logger1'].info(
            "/user/reset_password : Response : " + str(dict(transaction_id=resp.transactionid, mobile=resp.mobile)))
        loggers['logger1'].info("/user/reset_password : status_code=200")
        return SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)


@app.post('/admin/add/doctor', response_model=DoctorSignupResponse, tags=["admin"])
async def doctor_signup(doctor: DoctorSignUpView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, user_id, msg = doctor_ctrl.signup(signup_doctormodel=doctor)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorSignupResponse(ayooid=user_id, email=resp.email, mobile=resp.mobile)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/signup', response_model=GetAdminDetailsViewStatus, tags=["admin"])
async def admin_signup(user: AdminSignUpView, token: str = Depends(oauth2_scheme_admin)):
    logger.info("/admin/signup")
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.signup(signup_viewmodel=user)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return GetAdminDetailsViewStatus(userid=resp.userid, email=resp.email, mobile=resp.mobile, password="",
                                             status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/login', response_model=AdminToken, tags=["admin"])
async def admin_login(login_view: UserLoginView):
    loggers['logger8'].info("/admin/login : request :" + str(dict(login_view)))
    logger.info("/admin/login")
    resp, msg = admin_ctrl.login(login_viewmodel=login_view)
    if not resp:
        loggers['logger8'].info("/admin/login : {status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=401, detail=msg)
    else:

        # r1: DBAdmin = resp
        access_token = create_access_token(userid=resp['userid'], admin_role_code=resp['admin_role_code'],
                                           admin_role=resp['admin_role'])
        loggers['logger8'].info("/admin/login : Response : " + str(
            dict(userid=resp['userid'], email=resp['email'], access_token=access_token, token_type='bearer',
                 admin_role_code=int(resp['admin_role_code']), admin_role=resp['admin_role'])))
        loggers['logger8'].info("/admin/login : status_code=200")

        return AdminToken(userid=resp['userid'], email=resp['email'], access_token=access_token, token_type='bearer',
                          admin_role_code=int(resp['admin_role_code']), admin_role=resp['admin_role'])


@app.post('/admin/clinic', response_model=ClinicViewResponse, tags=["admin"])
async def add_clinic(clinic: ClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/clinic : request :" + str(dict(clinic)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.addclinic(addclinic_viewmodel=clinic)
        if not resp:
            loggers['logger8'].info("/admin/clinic : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info(
                "/admin/clinic : Response : " + str(dict(name=resp.name, address=resp.address, status=msg)))
            loggers['logger8'].info("/admin/clinic : status_code=200")
            return ClinicViewResponse(name=resp.name, address=resp.address, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/doctor/login', response_model=DoctorLoginToken, tags=["doctor"])
async def doctor_login(login_view: DoctorLoginView):
    logger.info("/doctor/login")
    resp, msg = doctor_ctrl.login(login_view_model=login_view)
    if not resp:
        raise HTTPException(status_code=401, detail=msg)
    else:
        r1: DBDoctor = resp
        access_token = create_access_token(userid=r1.doctorid, role='doctor')
        return DoctorLoginToken(ayooid=r1.ayooid, doctorid=r1.doctorid, email=r1.email, access_token=access_token,
                                token_type='bearer')


@app.post('/doctor/logout', tags=["doctor"])
async def doctor_logout(
        body: LogoutRequest = Body(None),
        token: str = Depends(oauth2_scheme_doctor)):
    logger.info("/doctor/logout")
    doctorid = get_valid_doctor_from_token(token=token)
    if not doctorid:
        raise HTTPException(status_code=401, detail="Invalid Token")
    else:
        resp, error = doctor_ctrl.logout(doctorid, body)
        if resp is not None:
            loggers['logger1'].info("/doctor/logout : status_code=200")
            return resp
        else:
            loggers['logger1'].info("/doctor/logout failed with error:" + error)
            return error


@app.get('/doctor/me', response_model=DoctorSignUpView, tags=["doctor"])
async def get_doctor(token: str = Depends(oauth2_scheme_doctor)):
    logger.info("/doctor/me")
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.get_by_id(doctorid=doctorid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return DoctorSignUpView(doctorid=resp['doctorid'],
                                firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                                email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                                ayooid=resp['ayooid'], consulting_duration_virtual=resp['consulting_duration_virtual'],
                                consulting_duration_clinic=resp['consulting_duration_clinic'],
                                consulting_fees_virtual=resp['consulting_fees_virtual'],
                                consulting_fees_clinic=resp['consulting_fees_clinic'],
                                profilename=resp['profilename'], practice_area=resp['practice_area'],
                                interest_area=resp.get('interest_area', []),
                                consultation_symptoms=resp.get('consultation_symptoms', []),
                                doctortype=str(resp['doctortype']), specialization=str(resp['specialization']),
                                prescription_type=str(resp['prescription_type']),
                                specialization_field=str(resp['specialization_field']),
                                languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                                additional_qualification=resp['additional_qualification'],
                                fellowship=resp['fellowship'],
                                residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                                homeaddress=resp['homeaddress'], bio=resp['bio'],
                                working_hour_starts_at=resp['working_hour_starts_at'],
                                working_hour_ends_at=resp['working_hour_ends_at'],
                                family_doctor_active=resp['family_doctor_active_status'],
                                clinics_attached=resp['clinics_attached'],
                                image_id=resp['image_id'],
                                profile_image_url=resp['profile_image_url'],
                                virtual_consultation_and_fees=resp['virtual_consultation_and_fees'],
                                clinic_consultation_and_fees=resp['clinic_consultation_and_fees'],
                                display_sequence=resp.get('display_sequence', 0)
                                )


@app.post('/doctor/update', response_model=DoctorSignUpView, tags=["doctor"])
async def update_doctor(doctor: DoctorSignUpView, token: str = Depends(oauth2_scheme_doctor)):
    logger.info("/doctor/update")
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.update_by_id(doctorid=doctorid, doctor_data=doctor)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return DoctorSignUpView(doctorid=resp['doctorid'],
                                firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                                email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                                ayooid=resp['ayooid'], consulting_duration_virtual=resp['consulting_duration_virtual'],
                                consulting_duration_clinic=resp['consulting_duration_clinic'],
                                consulting_fees_virtual=resp['consulting_fees_virtual'],
                                consulting_fees_clinic=resp['consulting_fees_clinic'],
                                profilename=resp['profilename'], practice_area=resp['practice_area'],
                                interest_area=resp.get('interest_area', []),
                                consultation_symptoms=resp.get('consultation_symptoms', []),
                                doctortype=resp['doctortype'], specialization=resp['specialization'],
                                specialization_field=resp['specialization_field'],
                                languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                                additional_qualification=resp['additional_qualification'],
                                fellowship=resp['fellowship'],
                                residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                                homeaddress=resp['homeaddress'], bio=resp['bio'],
                                working_hour_starts_at=resp['working_hour_starts_at'],
                                working_hour_ends_at=resp['working_hour_ends_at'],
                                virtual_consultation_and_fees=resp['virtual_consultation_and_fees'],
                                clinic_consultation_and_fees=resp['clinic_consultation_and_fees'],
                                display_sequence=resp.get('display_sequence', 0)
                                )


@app.post('/doctor/mapclinic', response_model=DoctorAndClinicMapping, tags=["doctor"])
async def assign_clinic(map_clinic: DoctorAndClinicMapping, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    logger.info("/doctor/mapclinic")
    resp, msg = doctor_ctrl.map_clinic_and_doctor(
        mapping_data=map_clinic, doctorid=doctorid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return DoctorAndClinicMapping(clinicid=resp['clinicid'], doctorid=resp['doctorid'], sunday=resp['sunday'],
                                      monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
                                      thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])


@app.post('/doctor/create/virtual/slots', response_model=VirtualAppointmentBooking, tags=["doctor"])
async def assign_clinic(map_clinic: VirtualAppointment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger8'].info("/doctor/create/virtual/slots : request :" + str(dict(map_clinic)))
    logger.info("/doctor/create/virtual/slots")
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.create_virtual_slots(
        mapping_data=map_clinic, doctorid=doctorid)
    if not resp:
        loggers['logger8'].info("/doctor/create/virtual/slots : {status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger8'].info(
            "/doctor/create/virtual/slots : Response : " + str(dict(doctorid=resp['doctorid'], sunday=resp['sunday'],
                                                                    monday=resp['monday'], tuesday=resp['tuesday'],
                                                                    wednesday=resp['wednesday'],
                                                                    thursday=resp['thursday'], friday=resp['friday'],
                                                                    saturday=resp['saturday'])))
        loggers['logger8'].info("/doctor/create/virtual/slots : status_code=200")
        return VirtualAppointmentBooking(doctorid=resp['doctorid'], sunday=resp['sunday'],
                                         monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
                                         thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])


# FIXME: Remove when tested the new one
# @app.post('/admin/mapclinic', response_model=DoctorAndClinicMapping, tags=["admin"])
# async def assign_clinic(map_clinic: DoctorAndClinicMapping, token: str = Depends(oauth2_scheme_admin)):
#     logger.info("/admin/mapclinic")
#     userid = get_valid_admin_from_token(token=token)
#     loggers['logger8'].info("/admin/mapclinic : request :" + str(dict(map_clinic)) + "by " + str(userid))
#     admin_check = admin_ctrl.get_admin_by_id(userid)
#     if admin_check is not None:
#         resp, msg = doctor_ctrl.map_clinic_and_doctor(mapping_data=map_clinic)
#         if not resp:
#             loggers['logger8'].info("/admin/mapclinic : {status_code=409, detail= " + str(msg) + "}")
#             raise HTTPException(status_code=409, detail=msg)
#         else:
#             loggers['logger8'].info("/admin/mapclinic : Response : " + str(
#                 dict(clinicid=resp['clinicid'], doctorid=resp['doctorid'], sunday=resp['sunday'],
#                      monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
#                      thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])))
#             loggers['logger8'].info("/admin/mapclinic : status_code=200")
#             return DoctorAndClinicMapping(clinicid=resp['clinicid'], doctorid=resp['doctorid'], sunday=resp['sunday'],
#                                           monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
#                                           thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])
#     else:
#         raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/admin/form/login', tags=["admin"])
async def get_admin_login_page(request: Request):
    return templates.TemplateResponse("adminloginpage.html", {"request": request})


@app.post('/admin/form/login', tags=["admin"])
async def admin_login_form(request: Request, mobile_or_email: str = Form(...), password: str = Form(...)):
    global session
    logger.info("/admin/form/login")
    admin_dict = {"mobile_or_email": mobile_or_email, "password": password}
    admin_login_req = client_request.post(url='/admin/login', json=admin_dict)
    admin_login_res = dict(admin_login_req.json())
    if 'detail' in admin_login_res.keys():
        return templates.TemplateResponse("adminloginpage.html", {"request": request,
                                                                  "errormessage": admin_login_res['detail']})
    else:
        session['token'] = admin_login_res["access_token"]
        return templates.TemplateResponse("index.html", {"request": request, "token": admin_login_res["access_token"]})


@app.get('/admin/form/index', tags=["admin"])
async def get_admin_index_page(request: Request):
    global session
    if 'token' in session.keys():
        if session['token'] is None:
            return RedirectResponse('/admin/form/login')
        else:
            return templates.TemplateResponse("index.html", {"request": request})
    else:
        return RedirectResponse('/admin/form/login')


@app.get('/admin/form/clinic', tags=["admin"])
async def get_admin_add_clinic_page(request: Request):
    global session
    if 'token' in session.keys():
        if session['token'] is None:
            return RedirectResponse('/admin/form/login')
        else:
            return templates.TemplateResponse("addclinic.html", {"request": request})
    else:
        return RedirectResponse('/admin/form/login')


@app.post('/admin/form/clinic', tags=["admin"])
async def admin_add_clinic_form(request: Request, clinicname: str = Form(...), address: str = Form(...),
                                latitude: float = Form(...), longitude: float = Form(...), services: str = Form(...),
                                city: str = Form(...)):
    global session
    clinic_dict = {"name": clinicname, "address": address, "lat": latitude, "lon": longitude, "services": services,
                   "city": city}
    session_token = session['token']
    header = {"Authorization": "bearer {}".format(session_token)}
    admin_add_clinic_req = client_request.post(
        url='/admin/clinic', json=clinic_dict, headers=header)
    admin_add_clinic_res = dict(admin_add_clinic_req.json())
    logger.info(admin_add_clinic_res)
    if 'detail' in admin_add_clinic_res.keys():
        return templates.TemplateResponse("addclinic.html", {"request": request,
                                                             "errormessage": admin_add_clinic_res['detail']})
    else:
        return templates.TemplateResponse("addclinic.html", {"request": request,
                                                             "name": admin_add_clinic_res["name"],
                                                             "address": admin_add_clinic_res["address"],
                                                             "status": admin_add_clinic_res["status"]})


@app.get('/admin/form/doctor', tags=["admin"])
async def get_admin_add_doctor_page(request: Request):
    global session, languages, doctor_type, doctor_specialization, doctor_allied
    if 'token' in session.keys():
        if session['token'] is None:
            return RedirectResponse('/admin/form/login')
        else:
            logger.info(f"the token value is : {session['token']}")
            return templates.TemplateResponse("adddoctor.html", {"request": request, "languages": languages,
                                                                 "doctor_type": doctor_type,
                                                                 "doctor_specialization": doctor_specialization,
                                                                 "doctor_allied": doctor_allied})
    else:
        return RedirectResponse('/admin/form/login')


@app.post('/admin/form/doctor', tags=["admin"])
async def admin_add_doctor_form(request: Request, firstname: str = Form(...), lastname: str = Form(...),
                                dob: str = Form(...), email: str = Form(...), mobile: str = Form(...),
                                password: str = Form(...), gender: str = Form(...), profilename: str = Form(...),
                                doctortype: str = Form(...), specialization: str = Form(...),
                                languagesknown: List[str] = Form(...), graduation: str = Form(...),
                                masters: Optional[str] = Form(...), bio: List[str] = Form(...),
                                additional_qualification: Optional[str] = Form(...),
                                fellowship: Optional[str] = Form(...),
                                residency: Optional[str] = Form(...), experience: str = Form(...),
                                awards: Optional[str] = Form(...), practice_area: List[str] = Form(...),
                                interest_area: List[str] = Form(...),
                                consultation_symptoms: List[str] = Form(...),
                                homeaddress: Optional[str] = Form(...), degree: List[dict] = Form(...),
                                license: str = Form(...)):
    global session, languages, doctor_type, doctor_specialization, doctor_allied
    logger.info(f"firstname: {firstname}, lastname: {lastname}, dob: {dob}, email: {email}, mobile: {mobile},"
                f"password: {password}, gender: {gender}, profilename: {profilename}, doctortype: {doctortype},"
                f"specialization: {specialization}, languagesknown: {languagesknown}, graduation: {graduation},"
                f"masters: {masters}, additional_qualification: {additional_qualification}, fellowship: {fellowship},"
                f"residency: {residency}, experience: {experience}, awards: {awards}, homeaddress: {homeaddress},"
                f"practice_area: {practice_area}, bio: {bio}")
    doctor_dict = {"firstname": firstname, "lastname": lastname, "dob": dob, "email": email, "mobile": mobile,
                   "password": password, "gender": gender, "profilename": profilename, "doctortype": doctortype,
                   "specialization": specialization, "languages": languagesknown, "graduation": graduation,
                   "masters": masters, "additional_qualification": additional_qualification, "fellowship": fellowship,
                   "residency": residency, "experience": experience, "awards": awards, "homeaddress": homeaddress,
                   "practice_area": practice_area, "interest_area": interest_area,
                   "consultation_symptoms": consultation_symptoms, "bio": bio, "degree": degree, "license": license}
    session_token = session['token']
    header = {'Authorization': 'bearer {}'.format(session_token)}
    admin_add_doctor_req = client_request.post(
        url='/admin/add/doctor', json=doctor_dict, headers=header)
    admin_add_doctor_res = dict(admin_add_doctor_req.json())
    logger.info(admin_add_doctor_res)
    if 'detail' in admin_add_doctor_res.keys():
        return templates.TemplateResponse("adddoctor.html", {"request": request, "languages": languages,
                                                             "doctor_type": doctor_type,
                                                             "doctor_specialization": doctor_specialization,
                                                             "doctor_allied": doctor_allied,
                                                             "errormessage": admin_add_doctor_res['detail']})
    else:
        return templates.TemplateResponse("adddoctor.html", {"request": request, "languages": languages,
                                                             "doctor_type": doctor_type,
                                                             "doctor_specialization": doctor_specialization,
                                                             "doctor_allied": doctor_allied,
                                                             "ayooid": admin_add_doctor_res["ayooid"],
                                                             "email": admin_add_doctor_res["email"],
                                                             "mobile": admin_add_doctor_res["mobile"]})


@app.get('/admin/form/logout', tags=["admin"])
async def admin_form_logout(request: Request):
    global session
    if 'token' in session.keys():
        session['token'] = None
    else:
        session = {}
    return RedirectResponse('/admin/form/login')


@app.post('/doctor/forgot_password', response_model=SignupResponse, tags=["doctor"])
async def reset_password(login_view: DoctorLoginView):
    logger.info("/doctor/forgot_password")
    resp, msg = doctor_ctrl.forgot_password(doctorlogin_viewmodel=login_view)
    if not resp:
        raise HTTPException(status_code=401, detail=msg)
    else:
        return SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)


@app.post('/doctor/verify_otp', response_model=DoctorVerifyOtpView, tags=["doctor"])
async def verify_otp(otp: VerifyOtpView):
    logger.info("/doctor/verify_otp")
    resp, msg = doctor_ctrl.verify_otp(otp_viewmodel=otp)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        r1: DBDoctor = resp
        return DoctorVerifyOtpView(firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.dob),
                                   email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender),
                                   ayooid=r1.ayooid)


@app.post('/doctor/change_password', response_model=DoctorVerifyOtpView, tags=["doctor"])
async def change_doctor_password(change_password_view: DoctorChangePasswordView,
                                 token: str = Depends(oauth2_scheme_doctor)):
    logger.info("/doctor/change_password")
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.change_password(
        doctorid=doctorid, change_password_view=change_password_view)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        r1: DBDoctor = resp
        return DoctorVerifyOtpView(firstname=r1.firstname, lastname=r1.lastname, dob=str(r1.dob),
                                   email=r1.email, mobile=r1.mobile, password='', gender=str(r1.gender),
                                   ayooid=r1.ayooid)


@app.post("/admin/add_health_history", response_model=PatientHealthHistoryView, tags=["admin"])
async def add_patient_health_history(patient: PatientHealthHistoryView, token: str = Depends(oauth2_scheme_admin)):
    logger.info("/admin/add_health_history")
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.add_patient_health_history(patient_view=patient)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                            pre_existing_conditions=resp['pre_existing_conditions'],
                                            family_history=resp['family_history'],
                                            any_additional_information=resp['any_additional_information'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post("/admin/get_health_history", response_model=PatientHealthHistoryView, tags=["admin"])
async def get_patient_health_history(patient: GetPatientHealthHistoryView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.get_patient_health_history(patient_view=patient)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                            pre_existing_conditions=resp['pre_existing_conditions'],
                                            family_history=resp['family_history'],
                                            any_additional_information=resp['any_additional_information'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post("/admin/update_health_history", response_model=PatientHealthHistoryView, tags=["admin"])
async def update_patient_health_history(patient: PatientHealthHistoryView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.update_patient_health_history(patient_view=patient)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                            pre_existing_conditions=resp['pre_existing_conditions'],
                                            family_history=resp['family_history'],
                                            any_additional_information=resp['any_additional_information'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/book/appointment/me', response_model=AppointmentBookingResponse, tags=["appointment"])
async def member_appointment(appointment_data: AppointmentBooking, booked_via: str = "Mobile App",
                             token: str = Depends(oauth2_scheme)):
    loggers['logger2'].info("book/appointment/me : request :" + str(dict(appointment_data)))
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    logger.info("/book/appointment/me")
    if userid is not None:
        if appointment_data.payment == "":
            raise HTTPException(status_code=422, detail='Invalid amount for appointment booking')
        resp, msg = ptn_ctrl.book_appointment(
            booking_data=appointment_data, userid=userid, booked_via=booked_via)
        if not resp:
            loggers['logger2'].info("/book/appointment/me : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:

            # logger.info(resp)
            loggers['logger2'].info("/book/appointment/me : Response : " + str(dict(resp)))
            loggers['logger2'].info("/book/appointment/me : status_code=200")
            return resp
    else:
        loggers['logger2'].info("/book/appointment/me : {status_code=409, detail= Invalid User}")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/book/appointment/others', response_model=AppointmentBookingResponse, tags=["appointment"])
async def others_appointment_by_member(appointment_data: AppointmentBookingForOthers,
                                       booked_via: str = "Mobile App",
                                       token: str = Depends(oauth2_scheme)):
    loggers['logger2'].info("/book/appointment/others : request :" + str(dict(appointment_data)))
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    logger.info("/book/appointment/others")
    if userid is not None:
        if appointment_data.payment == "":
            raise HTTPException(status_code=422, detail='Invalid amount for appointment booking')
        resp, msg = ptn_ctrl.book_appointment_others(
            booking_data=appointment_data, userid=userid, booked_via=booked_via)
        if not resp:
            loggers['logger2'].info("/book/appointment/others : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger2'].info("/book/appointment/others : Response : " + str(dict(resp)))
            loggers['logger2'].info("/book/appointment/others : status_code=200")
            return resp
    else:
        loggers['logger2'].info("/book/appointment/others : {status_code=409, detail= Invalid User}")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/book/appointment/guest',
          tags=["appointment"])
async def guest_appointment(appointment_data: GuestAppointmentBooking, booked_via: str = "Mobile App"):
    logger.info("/book/appointment/guest")
    resp, msg = ptn_ctrl.book_appointment_guest(booking_data=appointment_data, booked_via=booked_via)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/book/appointment/guest/others',
          tags=["appointment"])
async def guest_appointment_others(appointment_data: GuestAppointmentBookingForOthers, booked_via: str = "Mobile App"):
    logger.info("/book/appointment/guest/others")
    resp, msg = ptn_ctrl.book_appointment_guest_for_others(
        booking_data=appointment_data, booked_via=booked_via)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/doctor/my/appointments', tags=["doctor"])  # response_model=List[ResponseAppointmentList], tags=["doctor"])
async def get_doctors_appointments(request_data: RequestAppointmentList, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    logger.info("/doctor/my/appointments")
    if doctorid is not None:
        logger.info(f"/doctor/my/appointments, doctorid:{doctorid}")
        resp, msg = doctor_ctrl.get_doctors_appointments(
            doctorid=doctorid, request_data=request_data)
        logger.info(f"/doctor/my/appointments, response:{resp}, msg:{msg}")
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        logger.info(f"/doctor/my/appointments, Invalid Doctor token")
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/user/my/appointments', response_model=ResponseAppointmentsList, tags=["appointment"])
async def get_users_appointments(request_data: RequestAppointmentList, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    logger.info("/user/my/appointments")
    loggers['logger1'].info("/user/my/appointments : request : " + str(dict(request_data)) + "by " + str(userid))
    switch_account_status = False
    caretaker_id = None
    consent = False
    if userid is not None:
        if request_data.patient_id and len(request_data.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(userid, request_data.patient_id)
            relation_data = relative_ctrl.get_relation_data_by_caretaker_and_relative(userid, request_data.patient_id)
            consent = relation_data.consent
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                caretaker_id = userid
                userid = request_data.patient_id
                switch_account_status = True
        resp, msg = ptn_ctrl.get_members_appointments(
            userid=userid, switched_account_status=switch_account_status, caretaker_id=caretaker_id,
            request_data=request_data, consent=consent)
        if not resp:
            loggers['logger1'].info("/user/my/appointments : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            response = ResponseAppointmentsList(appointments=resp)
            loggers['logger1'].info("/user/my/appointments : status_code=200")
            return response
    else:
        loggers['logger1'].info("/user/my/appointments : {status_code=409, detail= Invalid User}")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/guest/my/appointments', response_model=ResponseAppointmentsList, tags=["appointment"])
async def get_guest_appointments(request_data: RequestGuestAppointmentList):
    resp, msg = ptn_ctrl.get_guest_appointments(request_data=request_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        response = ResponseAppointmentsList(appointments=resp)
        return response


@app.post('/appointment/search_clinic', response_model=SearchClinicResponse, tags=["appointment"])
async def search_clinic(search: SearchClinicView):
    resp, msg = ptn_ctrl.search_clinic(search_view=search)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return SearchClinicResponse(search_result=resp, search_status=msg)


# @app.get('/admin/insert_random_data', tags=["admin"])
# async def insert_random_data(token: str = Depends(oauth2_scheme_admin)):
#     userid = get_valid_admin_from_token(token=token)
#     admin_check = admin_ctrl.get_admin_by_id(userid)
#     if admin_check is not None:
#         resp = insert_random_data_ctrl.insert_data(doctor_allied=doctor_allied,
#                                                    doctor_specialization=doctor_specialization,
#                                                    languages=languages,
#                                                    client_request=client_request)
#         if not resp:
#             raise HTTPException(
#                 status_code=409, detail='Error occurred while inserting random data')
#         else:
#             return resp
#     else:
#         raise HTTPException(status_code=409, detail='Invalid Admin')

@app.post('/doctor/available/virtual/slots',
          tags=["doctor"])
async def get_doctors_virtual_aval_slot(request_data: RequestDoctorsVirtualAvailableSlots):
    user_id = None
    if request_data.user_id:
        user_id = request_data.user_id
    resp, msg = doctor_ctrl.get_virtual_slots_availability(request_data=request_data, user_id=user_id)
    if resp:
        return resp
    else:
        raise HTTPException(status_code=409, detail=msg)


@app.post('/admin/add_symptoms', response_model=AddSymptomsImageResponse, tags=["admin"])
async def add_symptoms(add_symptom: AddSymptomsImageView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.add_symptom(add_symptom_view=add_symptom)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddSymptomsImageResponse(symptom_id=resp['symptom_id'], symptom_name=resp['symptom_name'],
                                            active_status=resp['active_status'],
                                            symptom_image_url=resp['symptom_image_url'],
                                            symptom_image_hexcode=resp['symptom_image_hexcode'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_symptoms', response_model=AddSymptomsImageResponse, tags=["admin"])
async def update_symptoms(update_symptom: UpdateSymptomsView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.update_symptom(
            update_symptom_view=update_symptom)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddSymptomsImageResponse(symptom_id=resp['symptom_id'], symptom_name=resp['symptom_name'],
                                            active_status=resp['active_status'],
                                            symptom_image_url=resp['symptom_image_url'],
                                            symptom_image_hexcode=resp['symptom_image_hexcode'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/appointment/get_symptoms', response_model=GetSymptomsResponse, tags=["appointment"])
async def get_active_symptoms():
    resp, msg = admin_ctrl.get_symptoms()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return GetSymptomsResponse(symptoms=resp, status=msg)


@app.get('/appointment/get_all_symptoms', response_model=GetSymptomsResponse, tags=["appointment"])
async def get_all_symptoms():
    resp, msg = admin_ctrl.get_all_symptoms()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return GetSymptomsResponse(symptoms=resp, status=msg)


@app.post('/prescription/add', response_model=AddPrescriptionResponseCopy, tags=["prescription"])
async def add_prescription(request_data: AddPrescriptionRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = prscpt_ctrl.create_prescription(
            request_data=request_data, doctorid=doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddPrescriptionResponseCopy(caseid=resp['caseid'],
                                               userid=request_data.userid,
                                               doctorid=resp['doctorid'],
                                               subjective=resp['subjective'],
                                               objective=resp['objective'],
                                               assessment=resp['assessment'],
                                               medication=resp['medication'],
                                               medicine=resp['medicine'],
                                               procedure=resp['procedure'],
                                               notes=resp['notes'],
                                               created_by=resp['created_by'],
                                               created_at=resp['created_at'],
                                               updated_by=resp['updated_by'],
                                               updated_at=resp['updated_at'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/prescription/find', tags=["prescription"])
async def get_prescription(request_data: FindPrescriptionsRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = prscpt_ctrl.find_prescription(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/prescription/update', tags=["prescription"])
async def update_prescription(request_data: AddPrescriptionRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    if doctorid is not None:
        resp, msg = prscpt_ctrl.update_prescription(
            request_data=request_data, doctorid=doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.get('/admin/insert_image_data', tags=["admin"])
async def insert_random_data(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = insert_random_data_ctrl.insert_symptoms_data(
            client_request=client_request)
        if not resp:
            raise HTTPException(
                status_code=409, detail='Error occurred while inserting image data')
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/benefit/add', response_model=CreateBenefitResponse, tags=["ayoo membership"])
async def add_benefit(request_data: Benefits, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.create_benifits(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/benefit/all', response_model=List[GetAllBenefitResponse], tags=["ayoo membership"])
async def get_benefit(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.get_all_benifits()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/benefit/update', response_model=GetAllBenefitResponse, tags=["ayoo membership"])
async def update_benefit(request_data: GetAllBenefitResponse, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.update_benefit(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/membership/plan/add', response_model=GetAllMembershipPlans, tags=["ayoo membership"])
async def add_membership_plan(request_data: CreateAyooPlan, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.create_membership_plan(
            request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/membership/plan/all', response_model=List[GetAllMembershipPlans], tags=["ayoo membership"])
async def get_membership_plans(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.get_all_membership_plans()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/membership/plan/update', response_model=UpdateMembershipPlan, tags=["ayoo membership"])
async def update_membership_plan(request_data: UpdateMembershipPlan, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.update_membership_plan(
            request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/ui/users', tags=["dashboard"])
async def get_all_users(name: Optional[str] = Query(None),
                        mobile: Optional[str] = Query(None),
                        email: Optional[str] = Query(None),
                        sort_order: Optional[str] = Query(None),
                        isRegistered: Optional[bool] = Query(None),
                        page_no: int = Query(1),
                        page_size: int = Query(None),
                        token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        return ctrl.get_all_users(sort_filter_data=UIUsersView(
            name=name,
            mobile=mobile,
            email=email,
            sort_order=sort_order,
            isRegistered=isRegistered,
            page_no=page_no,
            page_size=page_size
        ))
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/ui/doctors', tags=["dashboard"])
async def get_all_doctors(token: str = Depends(oauth2_scheme_admin or oauth2_scheme_doctor or oauth2_scheme)):
    userid = get_valid_admin_from_token(token=token)
    resp, msg = doctor_ctrl.get_all_doctors()
    if userid is not None:
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/ui/clinics', tags=["dashboard"])
async def get_all_clinics(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_all_clinics()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/exist', tags=["user"])
async def check_user_exist(login_view: UserExistView):
    resp = ctrl.get_user_or_relative_by_email_or_mobile(
        login_or_email=login_view.mobile_or_email)
    if not resp:
        raise HTTPException(status_code=409, detail='User does not exists')
    else:
        return resp


@app.post('/user/feedback', response_model=PatientFeedbackForDoctorResponse, tags=["user"])
async def add_patient_feedback(feedback_view: PatientFeedbackForDoctor, token: str = Depends(oauth2_scheme)):
    userid = get_valid_admin_from_token(token=token)
    resp, msg = ptn_ctrl.add_feedback(
        add_feedback_view=feedback_view, user_id=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        r1: PatientFeedbackForDoctorResponse = resp
        return PatientFeedbackForDoctorResponse(patientid=r1.patientid, doctorid=r1.doctorid, caseid=r1.caseid,
                                                rating=r1.rating, date=r1.date, feedback=r1.feedback)


@app.post('/doctor/feedback', response_model=GetDoctorFeedbackResponseView, tags=["doctor"])
async def add_patient_feedback(feedback_view: GetDoctorFeedback):
    resp, msg = ptn_ctrl.get_feedback(get_feedback_view=feedback_view)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return GetDoctorFeedbackResponseView(total_feedback=resp, status=msg)


@app.post('/appointment/search_allclinic', response_model=SearchClinicResponse, tags=["appointment"])
async def search_all_clinic(search: SearchAllClinicView):
    resp, msg = ptn_ctrl.search_all_clinic(search_view=search)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return SearchClinicResponse(search_result=resp, search_status=msg)


@app.post('/appointment/search_clinicid', response_model=SearchClinicIdResponse, tags=["appointment"])
async def search_clinicid(search: SearchClinicIdView):
    resp, msg = ptn_ctrl.search_clinicid(search_view=search)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return SearchClinicIdResponse(search_result=resp, search_status=msg)


@app.get('/admin/insert_random_data_blr', tags=["admin"])
async def insert_random_data_blr(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = insert_random_data_ctrl.insert_data_bengaluru(doctor_allied=doctor_allied,
                                                             doctor_specialization=doctor_specialization,
                                                             languages=languages,
                                                             client_request=client_request)
        if not resp:
            raise HTTPException(
                status_code=409, detail='Error occurred while inserting random data')
        else:
            return resp


@app.post('/membership/subscription/add', response_model=CreateSubscriptionResponse, tags=["ayoo membership"])
async def add_subscription(request_data: CreateSubscriptionRequest, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.create_subscription(
            request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return CreateSubscriptionResponse(msg=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/find/subscription', response_model=FindSubscriptionResponse, tags=["ayoo membership"])
async def get_subscription(request_data: FindSubscription, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.find_subscription(
            request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/membership/find/subscription/me', response_model=FindSubscriptionResponse, tags=["ayoo membership"])
async def get_subscription(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        request_data = FindSubscription(userid=userid)
        resp, msg = membr_ctrl.find_subscription(
            request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/find/if/member', response_model=ResponseFindIfMember, tags=["ayoo membership"])
async def get_subscription(request_data: FindIfMember):
    resp, msg = membr_ctrl.check_if_member(
        request_data=request_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/find/if/registered/member', response_model=ResponseFindIfMember, tags=["ayoo membership"])
async def get_subscription(request_data: FindIfMemberByMobile):
    resp, msg = membr_ctrl.check_if_member_by_mobile_and_email(
        request_data=request_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/delete_clinic', response_model=ClinicViewResponse, tags=["admin"])
async def delete_clinic(clinic: DeleteClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.delete_clinic(clinic)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return ClinicViewResponse(name=resp.name, address=resp.address, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_clinic', response_model=ClinicViewResponse, tags=["admin"])
async def delete_clinic(clinic: UpdateClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.update_clinic(clinic)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return ClinicViewResponse(name=resp.name, address=resp.address, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_doctor', tags=["admin"])
async def update_doctor_admin(doctor: UpdateDoctorSignUpView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/update_doctor : request :" + str(dict(doctor)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.update_by_id_admin(doctorid=str(doctor.doctorid), doctor_data=doctor)
        if not resp:
            loggers['logger8'].info("/admin/update_doctor : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:

            loggers['logger8'].info("/admin/update_doctor : status_code=200")
            return dict(doctorid=resp['doctorid'],
                        firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                        email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                        ayooid=resp['ayooid'],
                        consulting_duration_virtual=resp['consulting_duration_virtual'],
                        consulting_duration_clinic=resp['consulting_duration_clinic'],
                        consulting_fees_virtual=resp['consulting_fees_virtual'],
                        consulting_fees_clinic=resp['consulting_fees_clinic'],
                        profilename=resp['profilename'], practice_area=resp['practice_area'],
                        interest_area=resp.get('interest_area', []),
                        consultation_symptoms=resp.get('consultation_symptoms', []),
                        doctortype=resp['doctortype'], specialization=resp['specialization'],
                        languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                        additional_qualification=resp['additional_qualification'],
                        fellowship=resp['fellowship'],
                        residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                        homeaddress=resp['homeaddress'], bio=resp['bio'], degree=resp['degree'],
                        license=resp['license'],
                        signature=resp['signature'],
                        working_hour_starts_at=resp['working_hour_starts_at'],
                        working_hour_ends_at=resp['working_hour_ends_at'],
                        virtual_consultation_and_fees=resp['virtual_consultation_and_fees'],
                        clinic_consultation_and_fees=resp['clinic_consultation_and_fees'],
                        display_sequence=resp.get('display_sequence', 0)
                        )
    else:
        loggers['logger8'].info("/admin/update_doctor : status_code=409, detail=Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_doctor', response_model=DoctorSignUpView, tags=["admin"])
async def update_doctor_admin(doctor: GetDoctorById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/get_doctor : request :" + str(dict(doctor)) + " by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.get_by_id(doctorid=doctor.doctorid)
        if not resp:
            loggers['logger8'].info("/admin/get_doctor : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info(f"/admin/get_doctor : Response : {resp}")
            loggers['logger8'].info("/admin/get_doctor : status_code=200")
            return DoctorSignUpView(doctorid=resp['doctorid'],
                                    firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                                    email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                                    ayooid=resp['ayooid'],
                                    consulting_duration_virtual=resp['consulting_duration_virtual'],
                                    consulting_duration_clinic=resp['consulting_duration_clinic'],
                                    consulting_fees_virtual=resp['consulting_fees_virtual'],
                                    consulting_fees_clinic=resp['consulting_fees_clinic'],
                                    profilename=resp['profilename'], practice_area=resp['practice_area'],
                                    interest_area=resp.get('interest_area', []),
                                    consultation_symptoms=resp.get('consultation_symptoms', []),
                                    doctortype=str(resp['doctortype']), specialization=str(resp['specialization']),
                                    specialization_field=resp['specialization_field'],
                                    languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                                    additional_qualification=resp['additional_qualification'],
                                    fellowship=resp['fellowship'],
                                    residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                                    homeaddress=resp['homeaddress'], bio=resp['bio'], degree=resp['degree'],
                                    license=resp['license'],
                                    signature=resp['signature'],
                                    working_hour_starts_at=resp['working_hour_starts_at'],
                                    working_hour_ends_at=resp['working_hour_ends_at'],
                                    family_doctor_active=resp['family_doctor_active_status'],
                                    clinics_attached=resp['clinics_attached'],
                                    image_id=resp['image_id'],
                                    profile_image_url=resp['profile_image_url'],
                                    virtual_consultation_and_fees=resp['virtual_consultation_and_fees'],
                                    clinic_consultation_and_fees=resp['clinic_consultation_and_fees'],
                                    display_sequence=resp.get('display_sequence', 0)
                                    )

    else:
        loggers['logger8'].info("/admin/get_doctor : status_code=409, detail=Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_doctor', response_model=DeleteDoctorResponseView, tags=["admin"])
async def delete_doctor_admin(doctor: GetDoctorById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.delete_doctor_by_id(doctorid=doctor.doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            r1: DBDoctor = resp
            return DeleteDoctorResponseView(doctorid=r1.doctorid, firstname=r1.firstname, status=msg)

    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/create_user', response_model=UserCreateView, tags=["admin"])
async def create_user_by_admin(user: CreateUserByAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ctrl.create_user_by_admin(signup_viewmodel=user)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            r1: DBUser = resp
            send_welcome_msg, msg_resp = chat_ctrl.welcome_msg_to_new_user(userid=r1.userid, registered_by='Admin')
            return UserCreateView(userid=r1.userid, mobile=r1.mobile, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_user', response_model=UserCreateView, tags=["admin"])
async def update_user_by_admin(user: UserUpdateBasicDetails, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ctrl.update_user(userid=user.userid, user_data=user)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return UserCreateView(userid=resp['userid'], mobile=resp['mobile'], status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_user', response_model=UserDetailsView, tags=["admin"])
async def get_user_by_admin(user: GetUserDetailViewRequest, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        user_details = ctrl.get_user_details(userid=user.userid, mongo=mongodb_conn)
        if user_details is not None:
            is_registered = user_details.get('is_registered', True)
        else:
            user_details = ctrl.get_relative_details(userid=user.userid, mongo=mongodb_conn)
            is_registered = False
            if not user_details:
                raise HTTPException(status_code=409, detail='user not found')
        return UserDetailsView(userid=user_details['userid'],
                               ayoo_id=user_details.get('ayoo_id', ''),
                               firstname=user_details['firstname'],
                               lastname=user_details['lastname'],
                               dob=str(user_details['birthdate']),
                               email=user_details['email'],
                               mobile=user_details['mobile'],
                               gender=str(user_details['gender']),
                               date_registered=user_details.get('date_registered'),
                               ayoo_member_id=user_details['ayoo_member_id'],
                               family_doctor=user_details['family_doctor'],
                               profile_image_url=user_details['profile_image_url'],
                               marital_status=user_details['marital_status'],
                               address=user_details['address'],
                               city=user_details['city'],
                               locality=user_details['locality'],
                               pin=user_details['pin'],
                               state=user_details['state'],
                               country=user_details['country'],
                               language1=user_details.get('language1'),
                               language2=user_details.get('language2'),
                               personal_doctor_name=user_details['personal_doctor_name'],
                               personal_doctor_phone=user_details['personal_doctor_phone'],
                               personal_doctor_email=user_details['personal_doctor_email'],
                               emergency_contacts=user_details.get('emergency_contacts'),
                               is_relative=is_registered,
                               caretaker_id=user_details.get('caretaker_id', None),
                               relationship=user_details.get('relationship', None)
                               )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_user', response_model=UserCreateView, tags=["admin"])
async def delete_user_by_admin(delete_data: DeleteUserByAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ctrl.delete_user(delete_data=delete_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            r1: DBUser = resp
            return UserCreateView(userid=r1.userid, mobile=r1.mobile, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_admin', response_model=GetAdminDetailsView, tags=["admin"])
async def get_admin(user: GetUserDetailViewRequest, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = admin_ctrl.get_admin_by_id(userid=user.userid)
        if not resp:
            raise HTTPException(status_code=409, detail="admin not found")
        else:
            r1: DBAdmin = resp
            return GetAdminDetailsView(userid=r1.userid, email=r1.email, mobile=r1.mobile, password="")
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_admin', response_model=GetAdminDetailsViewStatus, tags=["admin"])
async def get_admin(user: GetAdminDetailsView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.update_admin_by_admin(user_model=user)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            r1: DBAdmin = resp
            return GetAdminDetailsViewStatus(userid=r1.userid, email=r1.email, mobile=r1.mobile, password="",
                                             status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_admin', response_model=GetAdminDetailsViewStatus, tags=["admin"])
async def delete_admin__user_by_admin(user: GetUserDetailViewRequest, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.delete_admin_user_by_admin(usermodel=user)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            r1: DBAdmin = resp
            return GetAdminDetailsViewStatus(userid=r1.userid, email=r1.email, mobile=r1.mobile, password="",
                                             status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/admin/get_alladmin', tags=["admin"])
async def get_all_admin(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_all_admin()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_clinic', response_model=UpdateClinicView, tags=["admin"])
async def get_cllinic(clinic: DeleteClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = admin_ctrl.get_clinic_by_id(clinicid=clinic.clinicid)
        if not resp:
            raise HTTPException(status_code=409, detail="clinic not found")
        else:
            r1: DBClinic = resp
            return UpdateClinicView(clinicid=r1.clinicid, name=r1.name, address=r1.address, lat=r1.lat, lon=r1.lon,
                                    city=r1.city, mobile=r1.mobile, working_hours=dict(starts_at=r1.starts_at,
                                                                                       ends_at=r1.ends_at))
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_appointments', response_model=None, tags=["admin"])
async def admin_get_appointment(get_appt_view: GetAppointmentView, skip: int = 0, limit: int = 30):
    loggers['logger8'].info("/admin/get_appointments : request :" + str(dict(get_appt_view)))
    appointments, msg, total_appointments = ptn_ctrl.get_appointmets(
        get_appt_view=get_appt_view)

    if not appointments:
        loggers['logger8'].info(
            "/admin/get_appointments :status_code=409, detail= " + str(msg) + "}")
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger8'].info(
            "/admin/get_appointments : Response : Fetched {} appointments".format(len(appointments)))
        return dict(length=total_appointments, appointments=appointments)


@app.post('/update/appointment/status', response_model=None, tags=["appointment"])
async def update_appointment_status(app_view: UpdateAppointmentStatus):
    try:
        resp = ptn_ctrl.update_appointment_status(app_view=app_view)
        return resp
    except Exception as e:
        raise HTTPException(status_code=409, detail=f'Error occurred while updating appointment status: {str(e)}')


@app.post('/appointment_status/add', response_model=None, tags=["appointment"])
async def appointment_status_list(app_view: appointmentstatus):
    resp = ptn_ctrl.add_appointment_status(app_view=app_view)
    return resp


@app.post('/appointment_status/delete', response_model=None, tags=["appointment"])
async def appointment_status_delete(app_view: appointmentstatusdel):
    resp = ptn_ctrl.delete_appointment_status(app_view=app_view)
    return resp


@app.get('/appointment_status/list', response_model=None, tags=["appointment"])
async def appointment_status_list():
    resp = ptn_ctrl.list_appointment_status()
    return resp


@app.post('/appointment_reason/add', response_model=None, tags=["appointment"])
async def appointment_status_list(app_view: appointmentreason):
    resp = ptn_ctrl.add_appointment_reason(app_view=app_view)
    return resp


@app.post('/appointment_reason/delete', response_model=None, tags=["appointment"])
async def appointment_status_delete(app_view: appointmentreasondel):
    resp = ptn_ctrl.delete_appointment_reason(app_view=app_view)
    return resp


@app.get('/appointment_reason/list', response_model=None, tags=["appointment"])
async def appointment_status_list():
    resp = ptn_ctrl.list_appointment_reason()
    return resp


@app.post('/book/block_slot', response_model=SlotBlockingResponse, tags=['appointment'])
async def block_slots(appointment_data: SlotBlockingView):
    resp, *msg = ptn_ctrl.block_appointment(slotBlock_viewmodel=appointment_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/book/check_slot', response_model=CheckSlotStatus, tags=['appointment'])
async def check_slots(appointment_data: SlotBlockingView):
    resp, msg = ptn_ctrl.check_appointment_slot(check_model=appointment_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return CheckSlotStatus(slot_id=str(appointment_data.slot_id), block_slot=str(appointment_data.block_slot),
                               doctor_id=str(appointment_data.doctor_id), slot_status=str(resp))


@app.post('/admin/get_symptoms', response_model=AddSymptomsImageResponse, tags=["admin"])
async def get_symptom_by_id(get_symptom: GetSymptomsById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_symptoms_by_id(symptoms=get_symptom)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddSymptomsImageResponse(symptom_id=resp['symptom_id'], symptom_name=resp['symptom_name'],
                                            active_status=resp['active_status'],
                                            symptom_image_url=resp['symptom_image_url'],
                                            symptom_image_hexcode=resp['symptom_image_hexcode'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_symptoms', response_model=DeleteSymptomsImageResponse, tags=["admin"])
async def get_symptom_by_id(delete_symptom: GetSymptomsById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.delete_symptoms_by_id(symptoms=delete_symptom)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DeleteSymptomsImageResponse(symptom_id=resp['symptom_id'], symptom_name=resp['symptom_name'],
                                               status='Deleted')
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get_appointment', response_model=AppointmentBookingDetails, tags=["admin"])
async def get_symptom_by_id(get_appointment: GetAppointmentById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/get_appointment : request :" + str(dict(get_appointment)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_appointment_by_id(appointment=get_appointment)
        if not resp:
            loggers['logger8'].info("/admin/get_appointment : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info(
                "/admin/get_appointment : Response : " + str(dict(appointment_id=resp['appointment_id'],
                                                                  appointment_type=resp['appointment_type'],
                                                                  appointment_for=resp['appointment_for'],
                                                                  symptoms=resp['symptoms'],
                                                                  symptoms_audio_clip=resp['symptoms_audio_clip'],
                                                                  additional_notes=resp['additional_notes'],
                                                                  clinicid=resp['clinicid'],
                                                                  doctorid=resp['doctorid'],
                                                                  appointment_slot=resp['appointment_slot'],
                                                                  is_active=resp['is_active'],
                                                                  is_confirmed=resp['is_confirmed'],
                                                                  payment=resp['payment'], caseid=resp['caseid'],
                                                                  patient_id=resp['patient_id'],
                                                                  booked_by=resp['booked_by'],
                                                                  created_at=resp['created_at'])))
            loggers['logger8'].info("/admin/get_appointment : status_code=200")

            return AppointmentBookingDetails(appointment_id=resp['appointment_id'],
                                             appointment_type=resp['appointment_type'],
                                             appointment_for=resp['appointment_for'],
                                             symptoms=resp['symptoms'], symptoms_audio_clip=resp['symptoms_audio_clip'],
                                             additional_notes=resp['additional_notes'], clinicid=resp['clinicid'],
                                             doctorid=resp['doctorid'], appointment_slot=resp['appointment_slot'],
                                             is_active=resp['is_active'], is_confirmed=resp['is_confirmed'],
                                             payment=resp['payment'], caseid=resp['caseid'],
                                             patient_id=resp['patient_id'], booked_by=resp['booked_by'],
                                             created_at=resp['created_at'])
    else:
        loggers['logger8'].info("/admin/get_appointment : status_code=409, detail=Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/add/appointment', response_model=None, tags=["admin"])
async def add_appointment(add_appointment: AppointmentBookingByAdmin, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    loggers['logger2'].info(
        "/admin/add/appointment : request :" + str(dict(add_appointment)) + "booked by " + str(admin_id))
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if add_appointment.patientid:
        res = doctor_ctrl.get_patient_details(patientid=add_appointment.patientid)
        if not res:
            raise HTTPException(status_code=409, detail='Patient Detail not found')
    if admin_check is not None:
        resp, msg = ptn_ctrl.add_appointment_by_admin(booking_data=add_appointment, admin_id=admin_id)
        if not resp:
            loggers['logger2'].info("/admin/add/appointment : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            if (add_appointment.amount == "0"):
                promo_code = add_appointment.promo_code if add_appointment.promo_code else ""
                quick_book = One_Time_Payment(
                    user_id=add_appointment.patientid,
                    appointment_id=resp['appointment_id'],
                    amount=add_appointment.amount,
                    promo_code=promo_code
                )
                resp1, order_status_msg = order_status(data=quick_book)
                loggers['logger2'].info(f"resp1: {resp1}")

            # TODO: Remove not needed as payment link send separate
            # loggers['logger2'].info("/admin/add/appointment : Response : " + str(dict(resp)))
            # loggers['logger2'].info("/admin/add/appointment : status_code=200")
            # if not add_appointment.is_rescheduled_appointment:
            #     if add_appointment.promo_code and add_appointment.promo_code != "" and add_appointment.promo_code is not None:
            #         quick_book = One_Time_Payment(user_id=add_appointment.patientid,
            #                                       appointment_id=resp['appointment_id'],
            #                                       amount=add_appointment.amount, promo_code=add_appointment.promo_code)
            #     else:
            #         quick_book = One_Time_Payment(user_id=add_appointment.patientid,
            #                                       appointment_id=resp['appointment_id'],
            #                                       amount=add_appointment.amount)
            #     resp1, order_status_msg = order_status(data=quick_book)
            #     loggers['logger2'].info("/admin/add/appointment payment status:\n")
            #     loggers['logger2'].info(f"msg: {order_status_msg}")
            #     if order_status_msg is not None:
            #         # if response is failure from cc avenue, the appointment is being deleted completely
            #         ptn_ctrl.remove_appointment_by_admin(appointment_id=resp['appointment_id'])
            #         raise HTTPException(status_code=409, detail=order_status_msg)
            return resp
    else:
        loggers['logger2'].info("/admin/add/appointment : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_appointment', response_model=DeleteAppointmentResponseView, tags=["admin"])
async def delete_doctor_admin(appointment: AppointmentById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.delete_appointment_by_id(appointment_id=appointment.appointment_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DeleteAppointmentResponseView(appointment_id=resp['appointment_id'], patient_id=resp['patient_id'],
                                                 status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_doctor/slot_ends_on', tags=["admin"])
async def delete_doctor_ends_on(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/update_doctor/slot_ends_on : request : " + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.update_doctor_end_date()
        if not resp:
            loggers['logger8'].info("/admin/update_doctor/slot_ends_on : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/update_doctor/slot_ends_on : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/update_doctor/slot_ends_on : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/update_doctor/slot_ends_on : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post("/admin/update_appointment", response_model=AppointmentBookingDetailsCopy, tags=["admin"])
async def update_appointment_admin(appointment_data: AppointmentBookingDetailsCopy1,
                                   token: str = Depends(oauth2_scheme_admin)):
    loggers['logger8'].info("/admin/update_appointment : request : " + str(dict(appointment_data)))
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.update_appointment_by_id(update_appt_view=appointment_data)
        if not resp:
            loggers['logger8'].info("/admin/update_appointment : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/update_appointment : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/update_appointment : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/update_appointment : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_doctor/virtual_slot_ends_on', tags=["admin"])
async def delete_doctor_ends_on_virtual(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.update_doctor_end_date_virtual()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/admin/get_appointment/all', tags=["admin"])
async def admin_get_all_appointments(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/get_appointment/all : request : " + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.get_all_appointments()
        if not resp:
            loggers['logger8'].info("/admin/get_appointment/all : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/get_appointment/all : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/get_appointment/all : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/get_appointment/all : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor_clinic_mapping', tags=["admin"])
async def admin_get_all_doctor_clinic_mapping(doctor: GetDoctorFeedback, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/doctor_clinic_mapping : request :" + str(dict(doctor)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_doctor_clinic_info(doctor_id=str(doctor.doctorid))
        if not resp:
            loggers['logger8'].info("/admin/doctor_clinic_mapping : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/doctor_clinic_mapping : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/doctor_clinic_mapping : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/doctor_clinic_mapping : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/clinic_all_doctors', tags=["admin"])
async def admin_get_all_doctors_in_given_clinic(clinic: DeleteClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/clinic_all_doctors : request :" + str(dict(clinic)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        resp, msg = admin_ctrl.get_clinic_doctor_info(clinic_id=str(clinic.clinicid))
        if not resp:
            loggers['logger8'].info("/admin/clinic_all_doctors : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/clinic_all_doctors : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/clinic_all_doctors : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/doctor_clinic_mapping : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/clinic_doctor_availability', tags=["admin"])
async def admin_get_all_mapped_available_slots(search_view: CheckDoctorAvailableSlot,
                                               token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info(
        "/admin/clinic_doctor_availability : request :" + str(dict(search_view)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.get_clinic_doctor_availability(search_view=search_view)
        if not resp:
            loggers['logger8'].info("/admin/clinic_doctor_availability : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/clinic_doctor_availability : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/clinic_doctor_availability : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/clinic_doctor_availability : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/mapping_calendar', tags=["admin"])
async def admin_get_doctor_calendar_for_clinic(search_view: CheckMappingForClinic,
                                               token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_mapping_with_clinic_data(search_view=search_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/update_mapclinic', response_model=DoctorAndClinicMapping, tags=["admin"])
async def update_mapping_clinic(map_clinic: UpdateDoctorAndClinicMapping, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/update_mapclinic : request :" + str(dict(map_clinic)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.update_mapped_clinic_doctor(mapping_data=map_clinic)
        if not resp:
            loggers['logger8'].info("/admin/update_mapclinic : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/update_mapclinic : Response : " + str(
                dict(clinicid=resp['clinicid'], doctorid=resp['doctorid'], sunday=resp['sunday'],
                     monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
                     thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])))
            loggers['logger8'].info("/admin/update_mapclinic : status_code=200")
            return DoctorAndClinicMapping(clinicid=resp['clinicid'], doctorid=resp['doctorid'], sunday=resp['sunday'],
                                          monday=resp['monday'], tuesday=resp['tuesday'], wednesday=resp['wednesday'],
                                          thursday=resp['thursday'], friday=resp['friday'], saturday=resp['saturday'])
    else:
        loggers['logger8'].info("/admin/update_mapclinic : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_mapping_calendar', tags=["admin"])
async def admin_delete_doctor_calendar_for_clinic(search_view: CheckMappingForClinic,
                                                  token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.delete_by_mappingid(map=search_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return {"status": resp}
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_virtual_appointments', tags=["admin"])
async def admin_delete_doctor_calendar_for_clinic(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.delete_all_virtual_appointments()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return {"status": resp}
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/check/virtual_consultation/appointmentid', response_model=JitsiMeetingInfo, tags=['appointment'])
async def check_virtual_meeting_by_appointment_id(check_info: CheckJitsiMeetingAppointmentId):
    resp = jitsi_ctrl.check_meeting_using_appointment_id(appointment_id=str(check_info.appointment_id))
    if not resp:
        raise HTTPException(status_code=409, detail='no meeting information found for this appointment id')
    else:
        return JitsiMeetingInfo(meeting_id=resp['meeting_id'],
                                meeting_link=resp['meeting_link'],
                                meeting_code=resp['meeting_code'],
                                appointment_id=resp['appointment_id'],
                                case_id=resp['case_id'],
                                doctorid=resp['doctorid'],
                                patientid=resp['patientid'],
                                appointment_slot=resp['appointment_slot'])


@app.post('/check/virtual_consultation/meetingcode', response_model=JitsiMeetingInfo, tags=['appointment'])
async def check_virtual_meeting_by_meeting_code(check_info: CheckJitsiMeetingCode):
    resp = jitsi_ctrl.check_time_for_meeting_info(meeting_code=str(check_info.meeting_code))
    return JitsiMeetingInfo(meeting_id=resp.meeting_id,
                            meeting_link=resp.meeting_link,
                            meeting_code=resp.meeting_code,
                            appointment_id=resp.appointment_id,
                            case_id=resp.case_id,
                            doctorid=resp.doctorid,
                            patientid=resp.patientid,
                            appointment_slot=resp.appointment_slot,
                            role=resp.role,
                            role_name=resp.role_name,
                            patient_name=resp.patient_name,
                            doctor_name=resp.doctor_name,
                            slot_duration=resp.slot_duration

                            )


@app.post('/user/new_token', tags=['user'])
async def user_login_new(login_view_new: UserLoginView):
    resp, msg = ctrl.login_new(login_viewmodel=login_view_new)
    if not resp:
        raise HTTPException(status_code=401, detail=msg)
    r12: DBUser = resp
    payload_res = admin_ctrl.get_payload_from_id(userid=r12.userid)
    access_token_new = create_access_token_new(userid_new=r12.userid, payload=payload_res)

    return Token(userid=r12.userid, access_token=access_token_new, token_type='bearer')


@app.post("/user/insert_vitals", tags=["user"])
async def insert_user_vitals(vitals: PatientVitalsInfoDate, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.insert_patient_vitals(patient_vitals=vitals, user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post("/user/update_vitals", tags=["user"])
async def update_user_vitals(vitals: PatientVitalsInfoDateReadingId, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.update_patient_vitals(patient_vitals=vitals, user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.get("/user/vitals", response_model=PatientVitalsInfoWithBMI, tags=["user"])
async def get_user_vitals(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.get_patient_vitals(user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        bmi_read = None
        heart_rate_read = sorted(resp['heart_rate'], key=lambda i: i['reading_date']) if len(resp['heart_rate']) else []
        weight_read = sorted(resp['weight'], key=lambda i: i['reading_date']) if len(resp['weight']) else []
        blood_pressure_read = sorted(resp['blood_pressure'], key=lambda i: i['reading_date']) if len(
            resp['blood_pressure']) else []
        height_read = sorted(resp['height'], key=lambda i: i['reading_date']) if len(resp['height']) else []
        temperature_read = sorted(resp['temperature'], key=lambda i: i['reading_date']) if len(
            resp['temperature']) else []
        hba1c_read = sorted(resp['hba1c'], key=lambda i: i['reading_date']) if len(resp['hba1c']) else []
        return PatientVitalsInfoWithBMI(userid=resp['userid'],
                                        heart_rate=heart_rate_read,
                                        weight=weight_read,
                                        blood_pressure=blood_pressure_read,
                                        height=height_read,
                                        bmi=bmi_read,
                                        temperature=temperature_read,
                                        hba1c=hba1c_read)


@app.post('/new/user/my/appointments', response_model=ResponseAppointmentsList, tags=["appointment"])
async def get_users_appointments(request_data: RequestAppointmentList, token: str = Depends(oauth2_scheme_new)):
    payload_dict = get_payload_from_token(token=token)
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    switch_account_status = False
    caretaker_id = None
    if userid is not None:
        resp, msg = ptn_ctrl.get_members_appointments_new(
            userid=userid, switched_account_status=switch_account_status, caretaker_id=caretaker_id,
            payload_dict=payload_dict, request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            response = ResponseAppointmentsList(appointments=resp)
            return response
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/patient/upload_prescription', response_model=PrescriptionView, tags=["prescription"])
async def upload_prescription(add_view: UploadPrescriptionRequestView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.upload_patient_prescription(add_pres_view=add_view, user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PrescriptionView(prescription_id=resp['prescription_id'],
                                prescription_name=resp['prescription_name'],
                                prescription_image_url=resp['prescription_image_url'],
                                prescription_date=resp['prescription_date'],
                                prescription_upload_date=resp['prescription_upload_date'],
                                prescription_update_date=resp['prescription_update_date'])


@app.post('/patient/get_prescriptions', response_model=GetPatientPrescription, tags=["prescription"])
async def get_prescription(get_view: GetPatientReportView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.get_patient_prescriptions(case_id=str(get_view.caseid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return GetPatientPrescription(caseid=resp['caseid'], prescriptions=resp['prescriptions'])


@app.post('/patient/update_prescription', response_model=PrescriptionView, tags=["prescription"])
async def update_prescription(update_view: UpdatePrescriptionRequestView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.update_patient_prescription(update_pres_view=update_view, user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PrescriptionView(prescription_id=resp['prescription_id'],
                                prescription_name=resp['prescription_name'],
                                prescription_image_url=resp['prescription_image_url'],
                                prescription_date=resp['prescription_date'],
                                prescription_upload_date=resp['prescription_upload_date'],
                                prescription_update_date=resp['prescription_update_date'])


@app.post('/patient/delete_prescription', response_model=PrescriptionView, tags=["prescription"])
async def delete_prescription(delete_view: DeletePrescriptionRequestView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.delete_patient_prescription(delete_pres_view=delete_view, user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PrescriptionView(prescription_id=resp['prescription_id'],
                                prescription_name=resp['prescription_name'],
                                prescription_image_url=resp['prescription_image_url'],
                                prescription_date=resp['prescription_date'],
                                prescription_upload_date=resp['prescription_upload_date'],
                                prescription_update_date=resp['prescription_update_date'])


@app.post('/new/user/payload/add', response_model=AddPolicyPayloadResponse, tags=["RBAC Policy"])
async def add_payload(request_data: PayloadDict, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.add_policy(
            add_policy_view=request_data, userid=userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddPolicyPayloadResponse(primary_userid=resp['primary_userid'],
                                            relative_userid=resp['relative_userid'],
                                            relation_type=resp['relation_type'],
                                            permission=resp['permission'],
                                            updated_by=resp['updated_by'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/doctor/upload_profile_image', response_model=DoctorProfileImageView, tags=["doctor"])
async def get_doctor(profile_image: UploadDoctorProfileImages, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.upload_doctor_profile_image(profile_view=profile_image, doctor_id=str(doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorProfileImageView(
                image_id=resp['image_id'],
                profile_image_url=resp['profile_image_url']
            )
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/doctor/upload_signature_image', tags=["doctor"])
async def get_doctor(profile_image: UploadDoctorSignatureImages, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.upload_doctor_signature_image(profile_view=profile_image, doctor_id=str(doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/upload_doctor_signature', tags=["doctor"])
async def get_doctor(profile_image: UploadDoctorSignatureImages, token: str = Depends(oauth2_scheme_admin)):
    admin_check = get_valid_admin_from_token(token=token)
    # doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.upload_doctor_signature_image(profile_view=profile_image,
                                                              doctor_id=profile_image.doctor_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/doctor/profile_image', response_model=DoctorProfileImageView, tags=["doctor"])
async def get_doctor(token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.get_doctor_profile_image(doctor_id=str(doctorid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return DoctorProfileImageView(
            image_id=resp['image_id'],
            profile_image_url=resp['profile_image_url']
        )


@app.post('/doctor/delete_profile_image', response_model=DoctorProfileImageView, tags=["doctor"])
async def get_doctor(token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    resp, msg = doctor_ctrl.delete_doctor_profile_image(doctor_id=str(doctorid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return DoctorProfileImageView(
            image_id=resp['image_id'],
            profile_image_url=resp['profile_image_url']
        )


@app.post("/appointment/add_health_history", response_model=PatientHealthHistoryView, tags=["appointment"])
async def add_patient_health_history(patient: PatientHealthHistoryViewUser, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.add_patient_health_history_user(patient_view=patient, patient_id=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                        pre_existing_conditions=resp['pre_existing_conditions'],
                                        family_history=resp['family_history'],
                                        any_additional_information=resp['any_additional_information'])


@app.get("/appointment/get_health_history", response_model=PatientHealthHistoryView, tags=["appointment"])
async def get_patient_health_history(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.get_patient_health_history_user(patient_id=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                        pre_existing_conditions=resp['pre_existing_conditions'],
                                        family_history=resp['family_history'],
                                        any_additional_information=resp['any_additional_information'])


@app.post("/appointment/update_health_history", response_model=PatientHealthHistoryView, tags=["appointment"])
async def update_patient_health_history(patient: PatientHealthHistoryViewUser, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.update_patient_health_history_user(patient_view=patient, patient_id=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return PatientHealthHistoryView(patient_id=resp['patient_id'], common_allergies=resp['common_allergies'],
                                        pre_existing_conditions=resp['pre_existing_conditions'],
                                        family_history=resp['family_history'],
                                        any_additional_information=resp['any_additional_information'])


# Cancel Appointmemnt on user payment cancel activity
@app.post("/appointment/cancel", tags=["appointment"])
async def cancel_appointment(appoitment_data: AppointmentCancellationModel, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.cancel_appointment_from_retry(appoitment_data, userid)
    if resp:
        return {"status": "Success"}
    else:
        return msg


@app.post('/doctor/get_specialist', tags=["doctor"])
async def get_all_specialist_doctors(specialist_view: Optional[SearchDoctorsBasedOnSpecialization],
                                     token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=userid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.get_all_specialist_doctors(specialist=specialist_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/supporting_staff/create_staff', response_model=SupportingStaffSignUpInsert, tags=["admin"])
async def create_supporting_staff(member_view: SupportingStaffSignUpView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = sup_stf_ctrl.signup(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return SupportingStaffSignUpInsert(team_member_id=resp['team_member_id'],
                                               firstname=resp['firstname'],
                                               lastname=resp['lastname'],
                                               dob=resp['dob'],
                                               email=resp['email'],
                                               mobile=resp['mobile'],
                                               encrypted_password=resp['encrypted_password'],
                                               gender=resp['gender'],
                                               highest_qualification=resp['highest_qualification'],
                                               doctorid_list=resp['doctorid_list'],
                                               clinic_roles=resp['clinic_roles'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/supporting_staff/get_staff', response_model=SupportingStaffSignUpInsert, tags=["admin"])
async def get_supporting_staff(member_view: SupportingStaffGetView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = sup_stf_ctrl.get_supporting_staff_by_id(team_member_id=str(member_view.team_member_id))
        if not resp:
            raise HTTPException(status_code=409, detail='Supporting_staff_id not found')
        else:
            return SupportingStaffSignUpInsert(team_member_id=resp['team_member_id'],
                                               firstname=resp['firstname'],
                                               lastname=resp['lastname'],
                                               dob=resp['dob'],
                                               email=resp['email'],
                                               mobile=resp['mobile'],
                                               encrypted_password=resp['encrypted_password'],
                                               gender=resp['gender'],
                                               highest_qualification=resp['highest_qualification'],
                                               doctorid_list=resp['doctorid_list'],
                                               clinic_roles=resp['clinic_roles'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/admin/supporting_staff/get_all_staff', tags=["admin"])
async def get_all_supporting_staff(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = sup_stf_ctrl.get_all_staff_info()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/supporting_staff/update_staff', response_model=SupportingStaffSignUpInsert, tags=["admin"])
async def update_supporting_staff(member_view: SupportingStaffUpdateView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = sup_stf_ctrl.update_supporting_staff(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return SupportingStaffSignUpInsert(team_member_id=resp['team_member_id'],
                                               firstname=resp['firstname'],
                                               lastname=resp['lastname'],
                                               dob=resp['dob'],
                                               email=resp['email'],
                                               mobile=resp['mobile'],
                                               encrypted_password=resp['encrypted_password'],
                                               gender=resp['gender'],
                                               highest_qualification=resp['highest_qualification'],
                                               doctorid_list=resp['doctorid_list'],
                                               clinic_roles=resp['clinic_roles'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/supporting_staff/delete_staff', response_model=SupportingStaffSignUpInsert, tags=["admin"])
async def delete_supporting_staff(member_view: SupportingStaffGetView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = sup_stf_ctrl.delete_supporting_staff(team_member_id=str(member_view.team_member_id))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return SupportingStaffSignUpInsert(team_member_id=resp['team_member_id'],
                                               firstname=resp['firstname'],
                                               lastname=resp['lastname'],
                                               dob=resp['dob'],
                                               email=resp['email'],
                                               mobile=resp['mobile'],
                                               encrypted_password=resp['encrypted_password'],
                                               gender=resp['gender'],
                                               highest_qualification=resp['highest_qualification'],
                                               doctorid_list=resp['doctorid_list'],
                                               clinic_roles=resp['clinic_roles'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor/upload_profile_image', response_model=DoctorProfileImageView, tags=["admin"])
async def get_doctor(profile_image: UploadDoctorProfileImagesByAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    logger.info("inside post req")
    if admin_check is not None:
        logger.info('views-py if of admin check')
        resp, msg = doctor_ctrl.upload_doctor_profile_image_by_admin(profile_view=profile_image)
        if not resp:
            logger.info('views-py if of not resp')
            raise HTTPException(status_code=409, detail=msg)
        else:
            logger.info('views-py else of not resp')
            return DoctorProfileImageView(
                image_id=resp['image_id'],
                profile_image_url=resp['profile_image_url']
            )
    else:
        logger.info('views-py else of admin check')
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor/profile_image', response_model=DoctorProfileImageView, tags=["admin"])
async def get_doctor(doctor_view: GetDoctorById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.get_doctor_profile_image(doctor_id=str(doctor_view.doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorProfileImageView(
                image_id=resp['image_id'],
                profile_image_url=resp['profile_image_url']
            )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor/delete_profile_image', response_model=DoctorProfileImageView, tags=["admin"])
async def get_doctor(doctor_view: GetDoctorById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.delete_doctor_profile_image(doctor_id=str(doctor_view.doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorProfileImageView(
                image_id=resp['image_id'],
                profile_image_url=resp['profile_image_url']
            )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/family/create_family_policy', response_model=AddFamilyResponse, tags=["Family"])
async def create_family_policy(request_data: AddFamilyView):
    resp, msg = ptn_ctrl.create_family_policy(create_family_policy_view=request_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/create/virtual_slots_for_doctor', response_model=VirtualAppointmentBooking, tags=["admin"])
async def create_virtual_slots(map_clinic: VirtualAppointmentCopy, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.create_virtual_slots_admin(
            mapping_data=map_clinic)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return VirtualAppointmentBooking(doctorid=resp['doctorid'], sunday=resp['sunday'],
                                             monday=resp['monday'], tuesday=resp['tuesday'],
                                             wednesday=resp['wednesday'],
                                             thursday=resp['thursday'], friday=resp['friday'],
                                             saturday=resp['saturday'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post("/admin/append_end_date", tags=["admin"])
async def update_appointment_admin(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.append_end_date_field()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp, msg
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/get_specialist', tags=["user"])
async def get_all_specialist_doctors(specialist_view: Optional[SearchDoctorsBasedOnSpecialization]):
    resp, msg = doctor_ctrl.get_all_specialist_doctors(specialist=specialist_view)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/doctor/appointment/extend_end_date', response_model=ResponseCaseIdChange, tags=["doctor"])
async def extend_end_date_case_id(extend_view: ExtendEndDateOfCaseId, token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=userid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.extend_end_date_of_caseid(extend_view=extend_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return ResponseCaseIdChange(case_id=resp, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/doctor/appointment/close_active_case', response_model=ResponseCaseIdChange, tags=["doctor"])
async def close_active_case_id(case_view: CloseActiveCaseId, token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=userid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.close_active_caseid(active_view=case_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return ResponseCaseIdChange(case_id=resp, status=msg)
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post("/admin/all_appointment_by_caseid", tags=["admin"])
async def get_all_appointment_by_caseid(case_view: CloseActiveCaseId, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.get_all_appointment_caseid_list(active_view=case_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get("/user/all_relatives", tags=["user"])
async def get_all_relative_of_user(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = relative_ctrl.get_all_relative_for_caretaker(caretaker_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/doctor/get_available_open_slots', tags=['doctor'])
async def get_doctor_virtual_slots(request_data: RequestAvailableSlots, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    resp = doctor_ctrl.get_doctor_virtual_availability_slots(request_data=request_data, doctorid=doctorid)
    if not resp:
        raise HTTPException(status_code=409)
    else:
        return resp


@app.post('/admin/view/virtual_working_hours', tags=['admin'])
async def view_virtual_slots(request_data: RequestDoctorsVirtualSlots, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info(
        "/admin/view/virtual_working_hours : request :" + str(dict(request_data)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_virtual_slots(request_data=request_data)
        if not resp:
            loggers['logger8'].info("/admin/view/virtual_working_hours : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/view/virtual_working_hours : Response : " + str(dict(data=resp)))
            loggers['logger8'].info("/admin/view/virtual_working_hours : status_code=200")
            return resp
    else:
        loggers['logger8'].info("/admin/view/virtual_working_hours : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/family/code_gen', tags=['family'])
async def family_code_gen(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.generate_family_code(userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/update_clinic_additional_fields', tags=['admin'])
async def view_virtual_slots(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = admin_ctrl.update_all_clinic_mobile_and_working_hours()
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/all_clinic_all_info', tags=['admin'])
async def all_clinic_all_info():
    resp, msg = admin_ctrl.get_all_clinic_all_info()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/all_info_by_clinicid', tags=['admin'])
async def clinics_info(clinic_view: DeleteClinicView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.get_all_info_by_clinic_id(clinic_id=str(clinic_view.clinicid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/appointment/all_info_by_clinicid', tags=['admin'])
async def all_info_by_clinic_id(clinic_view: DeleteClinicView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = admin_ctrl.get_all_info_by_clinic_id(clinic_id=str(clinic_view.clinicid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/add_doctor_working_hour', tags=['admin'])
async def doctor_working_hour_add(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = admin_ctrl.add_doctor_working_hours()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/family/add_member', tags=['family'])
async def add_member_to_family(request_data: AddMemberToFamilyView, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.add_member_to_family(request_data=request_data, user_id=user_id)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/update/virtual_slots_for_doctor', response_model=VirtualAppointmentBooking, tags=["admin"])
async def update_virtual_slots(map_clinic: VirtualAppointmentCopy, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info(
        "/admin/update/virtual_slots_for_doctor : request :" + str(dict(map_clinic)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.update_virtual_slots_admin(
            mapping_data=map_clinic)
        if not resp:
            loggers['logger8'].info(
                "/admin/update/virtual_slots_for_doctor : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info("/admin/update/virtual_slots_for_doctor : Response : " + str(
                dict(doctorid=resp['doctorid'], sunday=resp['sunday'],
                     monday=resp['monday'], tuesday=resp['tuesday'],
                     wednesday=resp['wednesday'],
                     thursday=resp['thursday'], friday=resp['friday'],
                     saturday=resp['saturday'])))
            loggers['logger8'].info("/admin/update/virtual_slots_for_doctor : status_code=200")
            return VirtualAppointmentBooking(doctorid=resp['doctorid'], sunday=resp['sunday'],
                                             monday=resp['monday'], tuesday=resp['tuesday'],
                                             wednesday=resp['wednesday'],
                                             thursday=resp['thursday'], friday=resp['friday'],
                                             saturday=resp['saturday'])
    else:
        loggers['logger9'].error(
            "/admin/update/virtual_slots_for_doctor: status_code=409, error occured as : Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete/virtual_slots', response_model=DeleteVirtualSlotsResponse, tags=["admin"])
async def delete_doctor_admin(request_data: DeleteVirtualSlotsView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    loggers['logger8'].info("/admin/delete/virtual_slots : request :" + str(dict(request_data)) + "by " + str(userid))
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.delete_virtual_slots_admin(request_data=request_data)
        if not resp:
            loggers['logger8'].info("/admin/delete/virtual_slots : {status_code=409, detail= " + str(msg) + "}")
            raise HTTPException(status_code=409, detail=msg)
        else:
            loggers['logger8'].info(
                "/admin/delete/virtual_slots : Response : " + str(dict(doctorid=resp['doctorid'], status=msg)))
            loggers['logger8'].info("/admin/delete/virtual_slots : status_code=200")
            return DeleteVirtualSlotsResponse(doctorid=resp['doctorid'], status=msg)
    else:
        loggers['logger9'].error("/admin/delete/virtual_slots : status_code=409, error occured as : Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/family/add_relative', tags=['family'])
async def add_relative_family(request_data: AddRelativeToFamilyView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.add_relative_to_family(request_data=request_data, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/create_family', tags=['family'])
async def create_new_family(request_data: CreateNewFamilyView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.create_new_family(request_data=request_data, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/user/add_new_relative', tags=['family'])
async def add_relative_family(request_data: AddRelativeToFamilyView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.add_new_member_to_family(request_data=request_data, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.get('/family/user/my_families', tags=['family'])
async def family_code_gen(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.get_user_families(userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/user/get_access_to_relative', tags=['family'])
async def family_code_gen(request_data: DataAccessView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.give_data_access(request_data=request_data, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/user/get_relative_token', tags=['family'])
async def family_code_gen(request_data: GetRelativeToken, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    relative_userid = request_data.relative_userid
    resp, msg = ptn_ctrl.check_if_access(access_from=relative_userid, access_to=userid)

    if not resp:
        raise HTTPException(status_code=409, detail=msg)

    elif resp is False:
        raise HTTPException(status_code=409, detail='No access')

    else:
        access_token = create_access_token(userid=relative_userid, role='user')
        return GetRelativeTokenResponse(bearer_token=access_token,
                                        token_type='bearer')


@app.post('/family/admin/add_new_relative', tags=['family'])
async def add_admin_relative_family(request_data: AdminAddRelativeToFamilyView,
                                    token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=admin_id)
    if admin_check is not None:
        resp, msg = ptn_ctrl.admin_add_new_member_to_family(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/upload_profile_image', tags=["user"])
async def get_user(profile_image: UploadPatientProfileImages, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(userid=userid)
    if user_check is not None:
        resp, msg = ptn_ctrl.upload_user_profile_image(profile_view=profile_image, user_id=str(userid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/doctor/update/family_doctor_active_status', tags=["doctor"])
async def update_family_doctor_active_status(family_doctor: FamilyDoctorStatus,
                                             token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=userid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.update_family_doctor_status(doctor_id=str(userid),
                                                            family_doctor=family_doctor.family_doctor_active)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.get('/doctor/family_doctor_active_status', tags=["doctor"])
async def get_family_doctor_active_status(token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=userid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.get_family_doctor_status(doctor_id=str(userid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/update/family_doctor_active_status', tags=["admin"])
async def update_family_doctor_active_status_admin(family_doctor: FamilyDoctorStatusAdmin,
                                                   token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.update_family_doctor_status(doctor_id=str(family_doctor.doctorid),
                                                            family_doctor=family_doctor.family_doctor_active)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/family_doctor_active_status', tags=["admin"])
async def update_family_doctor_active_status_admin(doctor: GetDoctorById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.get_family_doctor_status(doctor_id=str(doctor.doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/delete_profile_image', tags=["user"])
async def get_user(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.delete_user_profile_image(user_id=str(userid))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/check_if_access', tags=['family'])
async def check_if_data_access(request_data: CheckIFAccessRequest):
    resp, msg = ptn_ctrl.check_if_access(access_from=str(request_data.access_from),
                                         access_to=str(request_data.access_to))
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/admin/create_relative_code', tags=['family'])
async def admin_create_reative_code(request_data: AdminFamilyCodeGenRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(adminid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.generate_family_code(userid=request_data.userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp


@app.post('/admin/clinic_team/add_member', response_model=ClinicTeamAdd, tags=["admin"])
async def create_clinic_team_member(member_view: SupportingStaffAddToClinic, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        valid_member = sup_stf_ctrl.get_supporting_staff_by_id(member_view.staff_id)
        valid_clinic = admin_ctrl.get_clinic_by_id(member_view.clinic_id)

        if not valid_member:
            raise HTTPException(status_code=409, detail='Invalid member id')
        elif not valid_clinic:
            raise HTTPException(status_code=409, detail='Invalid clinic id')

        resp, msg = clinic_team_ctrl.add_member(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)

        else:
            return ClinicTeamAdd(staff_id=resp['staff_id'],
                                 clinic_id=resp['clinic_id'],
                                 roles=resp['roles'])
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/family/admin/get_all_families', tags=['family'])
async def admin_get_all_families(token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(adminid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.admin_get_all_families()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp


@app.post('/admin/update/all_records_family_doctor_active_status', tags=["admin"])
async def update_all_family_doctor_active_status_admin(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.update_all_records_doctors_family()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp


@app.post('/admin/clinic_team/get_all_member', response_model=ClinicTeamView, tags=["admin"])
async def get_all_clinic_team_member(member_view: ClinicTeamGetView, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        valid_clinic = admin_ctrl.get_clinic_by_id(member_view.clinic_id)

        if not valid_clinic:
            raise HTTPException(status_code=409, detail='Invalid clinic id')
        resp, msg = clinic_team_ctrl.get_all_team_info(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return ClinicTeamView(clinic_id=member_view.clinic_id,
                                  clinic_members=resp
                                  )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/family/admin/get_user_families', tags=['family'])
async def admin_get_user_families(request_data: AdminGetUserFamilyRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.admin_get_user_families(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/member_login', tags=["AyooSupportingMember"])
async def supporting_member_login(login_view: UserLoginView):
    resp, msg = admin_ctrl.member_login(login_viewmodel=login_view)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        resp['access_token'] = create_access_token(userid=resp['access_token'])
        return resp


@app.post('/family/user/remove_member_from_family', tags=['family'])
def remove_member(request_data: RemoveFamilyMemberRequest, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = ptn_ctrl.remove_member_from_family(request_data=request_data, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/family/admin/remove_member_from_family', tags=['family'])
async def admin_remove_member(request_data: AdminRemoveFamilyMemberRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.admin_remove_member_from_family(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp


@app.post('/admin/clinic_team/delete_member', tags=["admin"])
async def delete_clinic_team_member(member_view: SupportingStaffAddToClinic, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        valid_member = sup_stf_ctrl.get_supporting_staff_by_id(member_view.staff_id)
        valid_clinic = admin_ctrl.get_clinic_by_id(member_view.clinic_id)

        if not valid_member:
            raise HTTPException(status_code=409, detail='Invalid member id')
        elif not valid_clinic:
            raise HTTPException(status_code=409, detail='Invalid clinic id')

        resp, msg = clinic_team_ctrl.delete_member(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)

        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/prescription/delete', tags=["prescription"])
async def delete_prescription(request_data: DeletePrescriptionRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = prscpt_ctrl.delete_prescription(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return f'Prescription with caseid {resp} deleted sucessfully'
    else:
        raise HTTPException(status_code=409, detail='Invalid doctor')


@app.post('/prescription/admin/add', response_model=AddPrescription, tags=["prescription"])
async def add_prescription(request_data: AdminAddPrescriptionRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(adminid)
    if admin_check is not None:
        resp, msg = prscpt_ctrl.create_prescription(
            request_data=AddPrescriptionRequest(userid=request_data.userid,
                                                caseid=request_data.caseid,
                                                subjective=request_data.subjective,
                                                objective=request_data.objective,
                                                assessment=request_data.assessment,
                                                medication=request_data.medication,
                                                procedure=request_data.procedure,
                                                notes=request_data.notes), doctorid=request_data.doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/prescription/admin/delete', tags=["prescription"])
async def delete_prescription(request_data: DeletePrescriptionRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(adminid)
    if admin_check is not None:
        resp, msg = prscpt_ctrl.delete_prescription(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp, f'All prescriptions by caseid {request_data.caseid} deleted sucessfully'
    else:
        raise HTTPException(status_code=409, detail='Invalid doctor')


@app.post('/prescription/admin/update', response_model=AddPrescriptionResponse, tags=["prescription"])
async def update_prescription(request_data: AdminAddPrescriptionRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    doctorid = request_data.doctorid
    admin_check = admin_ctrl.get_admin_by_id(adminid)
    if admin_check is not None:
        resp, msg = prscpt_ctrl.update_prescription(
            request_data=request_data, doctorid=doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/notification/add_device', response_model=DeviceInfoAdd, tags=['notification'])
async def add_device_info(device_info: DeviceInfoAdd):
    resp, msg = firebase_ctrl.add_device(device_info=device_info)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)

    else:
        return DeviceInfoAdd(device_id=resp['device_id'],
                             device_type=resp['device_type'],
                             fcm_token=resp['fcm_token'],
                             )


@app.post('/notification/add_device_user', response_model=UserDeviceInfoView, tags=['notification'])
async def add_user_device_info(device_info: UserDeviceInfoAdd,
                               token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    user_type = device_info.user_type.value
    user_id = None
    if user_type == 'User':
        loggers['logger1'].info("/notification/add_device_user : User request : " + str(dict(device_info)))
        userid = get_valid_user_from_token(token=token, ctrl=ctrl)
        user_check = ctrl.get_user_by_id(userid)
        if user_check is not None:
            user_id = user_check.userid

    if user_type == 'Doctor':
        loggers['logger1'].info("/notification/add_device_user : Doctor request : " + str(dict(device_info)))
        doctorid = get_valid_doctor_from_token(token=token)
        doctor_check = doctor_ctrl.get_doctor_by_id(doctorid)
        if doctor_check is not None:
            user_id = doctor_check.doctorid

    if user_id:
        resp, msg = firebase_ctrl.add_device_user(device_info=device_info, user_id=user_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)

        else:
            loggers['logger1'].info(
                "/notification/add_device_user : response : " + str(dict(device_id=resp['device_id'],
                                                                         device_type=resp['device_type'],
                                                                         fcm_token=resp['fcm_token'],
                                                                         user_id=resp['user_id'],
                                                                         user_type=resp['user_type'],
                                                                         last_login=resp['last_login']
                                                                         )))
            loggers['logger1'].info("/notification/add_device_user : status_code : 200")
            return UserDeviceInfoView(device_id=resp['device_id'],
                                      device_type=resp['device_type'],
                                      fcm_token=resp['fcm_token'],
                                      user_id=resp['user_id'],
                                      user_type=resp['user_type'],
                                      last_login=resp['last_login']
                                      )
    else:
        loggers['logger1'].info(f'/notification/add_device_user status_code=409, detail=Invalid {user_type}')
        raise HTTPException(status_code=409, detail=f'Invalid {user_type}')


@app.post('/notification/get_active_device', response_model=UserActiveDeviceView, tags=['notification'])
async def get_active_device(user_type: UserActiveDeviceGet,
                            token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    user_type = user_type.user_type.value
    user_id = None
    if user_type == 'User':
        userid = get_valid_user_from_token(token=token, ctrl=ctrl)
        user_check = ctrl.get_user_by_id(userid)
        if user_check is not None:
            user_id = user_check.userid

    if user_type == 'Doctor':
        doctorid = get_valid_doctor_from_token(token=token)
        doctor_check = doctor_ctrl.get_doctor_by_id(doctorid)
        if doctor_check is not None:
            user_id = doctor_check.doctorid

    if user_id:
        resp, msg = firebase_ctrl.get_active_device(user_id=user_id, user_type=user_type)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)

        else:
            return UserActiveDeviceView(user_id=resp['user_id'],
                                        user_type=resp['user_type'],
                                        device_info=resp['device_info']
                                        )
    else:
        raise HTTPException(status_code=409, detail=f'Invalid {user_type}')


@app.post('/notification/remove_active_device', response_model=UserActiveDeviceView, tags=['notification'])
async def remove_active_device(user_info: UserDeviceInfoAdd,
                               token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    user_type = user_info.user_type.value
    user_id = None
    if user_type == 'User':
        userid = get_valid_user_from_token(token=token, ctrl=ctrl)
        user_check = ctrl.get_user_by_id(userid)
        if user_check is not None:
            user_id = user_check.userid

    if user_type == 'Doctor':
        doctorid = get_valid_doctor_from_token(token=token)
        doctor_check = doctor_ctrl.get_doctor_by_id(doctorid)
        if doctor_check is not None:
            user_id = doctor_check.doctorid

    if user_id:
        resp, msg = firebase_ctrl.remove_active_device(user_info=user_info, user_id=user_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)

        else:
            return UserActiveDeviceView(user_id=resp['user_id'],
                                        user_type=resp['user_type'],
                                        device_info=resp['device_info']
                                        )
    else:
        raise HTTPException(status_code=409, detail=f'Invalid {user_type}')


@app.post('/admin/doctor_team/add_member', response_model=DoctorTeamMembers, tags=['admin'])
async def add_doctor_team_member(member_view: TeamMemberAddToDoctor, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = doctor_team_ctrl.add_member(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorTeamMembers(doctor_id=resp['doctor_id'],
                                     doctor_team=resp['doctor_team']
                                     )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor_team/get_member', response_model=TeamMemberInfo, tags=['admin'])
async def get_doctor_team_member_info(member_view: DoctorTeamGetMember, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        resp, msg = doctor_team_ctrl.team_member_info(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return TeamMemberInfo(team_member_id=resp['team_member_id'],
                                  team_member_role=resp['team_member_role'],
                                  doctor_list=resp['doctor_list']
                                  )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor_team/get_all_member', response_model=DoctorTeamMembers, tags=['admin'])
async def get_all_doctor_team_member(member_view: DoctorTeamGet, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        resp, msg = doctor_team_ctrl.get_all_team_info(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorTeamMembers(doctor_id=resp['doctor_id'],
                                     doctor_team=resp['doctor_team']
                                     )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor_team/delete_member', response_model=DoctorTeamMembers, tags=['admin'])
async def delete_doctor_team_member(member_view: TeamMemberAddToDoctor, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:

        resp, msg = doctor_team_ctrl.delete_member(member_view=member_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorTeamMembers(doctor_id=resp['doctor_id'],
                                     doctor_team=resp['doctor_team']
                                     )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/prescriptions/patientid/find', tags=["prescription"])
async def get_prescription(request_data: FindPrescriptionsRequest):
    resp, msg = prscpt_ctrl.find_prescription_patientid(request_data=request_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post("/user/subscription/buy", response_model=CreateSubscription, tags=["ayoo membership"])
async def subscription_buy_user(subscription_plan: UserSubscriptionPayment, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = membr_ctrl.user_subscription_buy(subscription_view=subscription_plan, userid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return CreateSubscription(subscriptionid=resp['subscriptionid'],
                                  userid=resp['userid'],
                                  planid=resp['planid'],
                                  subscribed_by=resp['subscribed_by'],
                                  members=resp['members'] if 'members' in resp.keys() else [],
                                  created_at=resp['created_at'],
                                  valid_till=resp['valid_till'],
                                  is_active=resp['is_active'],
                                  )


@app.post('/admin/benefit_by_id', response_model=GetAllBenefitResponse, tags=["ayoo membership"])
async def get_benefit_by_id(request_data: GetBenefitById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp: DBBenefits
        resp = membr_ctrl.get_benefits_by_id(benefitid=request_data.benefitid)
        if not resp:
            raise HTTPException(status_code=409, detail='benefitid does not exist')
        else:
            return GetAllBenefitResponse(bnfid=resp.bnfid,
                                         title=resp.title,
                                         description=resp.description,
                                         benefit=resp.benefit,
                                         is_active=resp.is_active
                                         )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/membership_plan_by_id', response_model=GetMembershipPlanByIdResponse, tags=["ayoo membership"])
async def membership_plan_by_id(request_data: GetMembershipPlanById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.get_membership_plan_with_benefit(planid=request_data.planid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return GetMembershipPlanByIdResponse(planid=resp['planid'],
                                                 title=resp['title'],
                                                 subtitle=resp['subtitle'],
                                                 fee=resp['fee'],
                                                 is_active=resp['is_active'],
                                                 benefits=resp['benefits']
                                                 )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_benefit_by_id', response_model=DeleteBenefitResponse, tags=["ayoo membership"])
async def delete_benefit_by_id(request_data: GetBenefitById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp: DBBenefits
        resp, msg = membr_ctrl.delete_by_benefit_id(benefitid=request_data.benefitid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DeleteBenefitResponse(bnfid=resp.bnfid,
                                         title=resp.title,
                                         description=resp.description,
                                         benefit=resp.benefit,
                                         is_active=resp.is_active,
                                         status=msg
                                         )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/delete_membership_plan_by_id', response_model=DeleteMembershipPlanResponse, tags=["ayoo membership"])
async def delete_membership_plan_by_id(request_data: GetMembershipPlanById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = membr_ctrl.delete_plan_by_id(planid=request_data.planid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DeleteMembershipPlanResponse(planid=resp['planid'],
                                                title=resp['title'],
                                                subtitle=resp['subtitle'],
                                                fee=resp['fee'],
                                                is_active=resp['is_active'],
                                                benefits=resp['benefits'],
                                                status=msg
                                                )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/add/family_doctor', tags=["family doctor"])
async def add_family_doctor(request_data: FamilyDoctorAdd, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    db_user: DBUser = ctrl.get_user_by_id(userid=userid)
    if db_user is not None:
        resp, msg = ptn_ctrl.add_family_doctor(doctor_id=request_data.doctor_id, user_id=userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/user/remove/family_doctor', tags=["family doctor"])
async def remove_family_doctor(request_data: FamilyDoctorAdd, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    db_user: DBUser = ctrl.get_user_by_id(userid=userid)
    if db_user is not None:
        resp, msg = ptn_ctrl.remove_family_doctor(doctor_id=request_data.doctor_id, user_id=userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/user/get/users_family_doctor', tags=["family doctor"])
async def users_family_doctor(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    db_user: DBUser = ctrl.get_user_by_id(userid=userid)
    if db_user is not None:
        resp, msg = ptn_ctrl.get_users_family_doctor(user_id=userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/prescription/user/find', tags=["prescription"])
async def get_prescription(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    resp, msg = prscpt_ctrl.find_prescription_user(patientid=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/prescription/user/find/caseid', tags=["prescription"])
async def get_prescription(request_data: FindPrescriptionCaseidRequest, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = prscpt_ctrl.find_prescription_user_caseid(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/doctor/get/doctors_family_doctor_details', tags=["family doctor"])
async def get_family_doctor(token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    if doctor_id is not None:
        resp, msg = ptn_ctrl.get_doctors_family_doctor_details(doctor_id=doctor_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/add/family_doctor', tags=["family doctor"])
async def add_family_doctor(request_data: FamilyDoctorAddByAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.add_family_doctor(doctor_id=request_data.doctor_id, user_id=request_data.user_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/remove/family_doctor', tags=["family doctor"])
async def remove_family_doctor(request_data: FamilyDoctorAddByAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.remove_family_doctor(doctor_id=request_data.doctor_id, user_id=request_data.user_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get/users_family_doctor', tags=["family doctor"])
async def users_family_doctor(request_data: GetUserById, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.get_users_family_doctor(user_id=request_data.userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/get/doctors_family_doctor_details', tags=["family doctor"])
async def add_family_doctor(request_data: FamilyDoctorAdd, token: str = Depends(oauth2_scheme)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp, msg = ptn_ctrl.get_doctors_family_doctor_details(doctor_id=request_data.doctor_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp


@app.post('/doctor/get_doctor', response_model=DoctorSignUpView, tags=["doctor"])
async def get_doctor_by_doctor(doctor: GetDoctorById, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.get_by_id(doctorid=doctor.doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return DoctorSignUpView(doctorid=resp['doctorid'],
                                    firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                                    email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                                    ayooid=resp['ayooid'],
                                    consulting_duration_virtual=resp['consulting_duration_virtual'],
                                    consulting_duration_clinic=resp['consulting_duration_clinic'],
                                    consulting_fees_virtual=resp['consulting_fees_virtual'],
                                    consulting_fees_clinic=resp['consulting_fees_clinic'],
                                    profilename=resp['profilename'], practice_area=resp['practice_area'],
                                    interest_area=resp.get('interest_area', []),
                                    consultation_symptoms=resp.get('consultation_symptoms', []),
                                    doctortype=str(resp['doctortype']), specialization=str(resp['specialization']),
                                    specialization_field=str(resp['specialization_field']),
                                    languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                                    additional_qualification=resp['additional_qualification'],
                                    fellowship=resp['fellowship'],
                                    residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                                    homeaddress=resp['homeaddress'], bio=resp['bio'], degree=resp['degree'],
                                    license=resp['license'],
                                    signature=resp['signature'],
                                    working_hour_starts_at=resp['working_hour_starts_at'],
                                    working_hour_ends_at=resp['working_hour_ends_at'],
                                    family_doctor_active=resp['family_doctor_active_status'],
                                    clinics_attached=resp['clinics_attached'],
                                    image_id=resp['image_id'],
                                    profile_image_url=resp['profile_image_url'],
                                    display_sequence=resp.get('display_sequence', 0)
                                    )

    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/user/get_doctor', response_model=DoctorSignUpView, tags=["user"])
async def get_doctor_by_user(doctor: GetDoctorById):  # , token: str = Depends(oauth2_scheme)):
    # userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    # user_check = ctrl.get_user_by_id(userid=userid)
    # if user_check is not None:
    resp, msg = doctor_ctrl.get_by_id(doctorid=doctor.doctorid)
    logger.info("/user/get_doctor")
    # logger.info(resp)
    # if not resp:
    #   raise HTTPException(status_code=409, detail=msg)
    # else:
    return DoctorSignUpView(doctorid=resp['doctorid'],
                            firstname=resp['firstname'], lastname=resp['lastname'], dob=str(resp['dob']),
                            email=resp['email'], mobile=resp['mobile'], password='', gender=str(resp['gender']),
                            ayooid=resp['ayooid'],
                            consulting_duration_virtual=calcualte_appointment_duration(
                                resp['consulting_duration_virtual']),
                            consulting_duration_clinic=calcualte_appointment_duration(
                                resp['consulting_duration_clinic']),
                            consulting_fees_virtual=resp['consulting_fees_virtual'],
                            consulting_fees_clinic=resp['consulting_fees_clinic'],
                            profilename=resp['profilename'], practice_area=resp['practice_area'],
                            interest_area=resp.get('interest_area', []),
                            consultation_symptoms=resp.get('consultation_symptoms', []),
                            doctortype=str(resp['doctortype']), specialization=str(resp['specialization']),
                            specialization_field=str(resp['specialization_field']),
                            languages=resp['languages'], graduation=resp['graduation'], masters=resp['masters'],
                            additional_qualification=resp['additional_qualification'],
                            fellowship=resp['fellowship'],
                            residency=resp['residency'], experience=resp['experience'], awards=resp['awards'],
                            homeaddress=resp['homeaddress'], bio=resp['bio'], degree=resp['degree'],
                            license=resp['license'],
                            # signature=resp['signature'],
                            working_hour_starts_at=resp['working_hour_starts_at'],
                            working_hour_ends_at=resp['working_hour_ends_at'],
                            family_doctor_active=resp['family_doctor_active_status'],
                            clinics_attached=resp['clinics_attached'],
                            image_id=resp['image_id'],
                            profile_image_url=resp['profile_image_url'],
                            display_sequence=resp.get('display_sequence', 0)
                            )

    # else:
    #    raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/prescription/doctor/find/caseid', tags=["prescription"])
async def get_prescription(request_data: FindPrescriptionCaseidRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = prscpt_ctrl.find_prescription_user_caseid(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/doctor/book/appointment', response_model=DoctorAppointmentBookingResponse, tags=["appointment"])
async def member_appointment_by_doctor(appointment_data: DoctorAppointmentBooking,
                                       token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    if doctor_id is not None:
        resp, msg = ptn_ctrl.book_appointment_by_doctor(
            booking_data=appointment_data, doctor_id=doctor_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            resp = dict(resp)
            return DoctorAppointmentBookingResponse(patient_name=resp['patient_name'],
                                                    caseid=resp['caseid'],
                                                    appointment_slot=resp['appointment_slot'],
                                                    reason_for_visit=resp['reason_for_visit'],
                                                    service_provider=resp['service_provider'],
                                                    payment=resp['payment']
                                                    )
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/prescription/admin/find/caseid', tags=["prescription"])
async def get_prescription(request_data: FindPrescriptionCaseidRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = prscpt_ctrl.find_prescription_user_caseid(request_data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/admin/patient_directory', tags=["admin"])
async def patient_directory(request_data: GetDoctorById, page_number: int = 1, page_size: Optional[int] = 16,
                            token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=userid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.patient_directory_of_doctors(doctor_id=str(request_data.doctorid),
                                                             page_number=page_number, page_size=page_size)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/doctor/patient_directory', tags=["doctor"])
async def patient_directory(page_number: int = 1, page_size: Optional[int] = 16,
                            token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.patient_directory_of_doctors(doctor_id=doctorid, page_number=page_number,
                                                             page_size=page_size)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/user/medicine_notification_toggle', tags=["user"])
async def med_notif_toggle_switch(request_data: AddPrescriptionResponse, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = ptn_ctrl.med_notif_toggle_switch(caseid=request_data.caseid, userid=userid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/doctor/doctor_clinic_mapping', tags=["doctor"])
async def get_all_doctor_clinic_mapping(token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = admin_ctrl.get_doctor_clinic_info(doctor_id=str(doctorid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/user/get_doctors_by_area_or_speciality', tags=["user"])
async def get_all_doctor_clinic_mapping(request_body: SearchByAreaOrSpecialization,
                                        token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = doctor_ctrl.get_doctors(request_body=request_body)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/doctor/get_doctors_by_area_or_speciality', tags=["doctor"])
async def get_all_doctor_clinic_mapping(request_body: SearchByAreaOrSpecialization,
                                        token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.get_doctors(request_body=request_body)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/get_doctors_by_area_or_speciality', tags=["admin"])
async def get_all_doctor_clinic_mapping(request_body: SearchByAreaOrSpecialization,
                                        token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.get_doctors(request_body=request_body)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/all_clinic_all_info', tags=['user'])
async def all_clinic_all_info(token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = admin_ctrl.get_all_clinic_all_info()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/user/all_info_by_clinicid', tags=['user'])
async def clinics_info(clinic_view: DeleteClinicView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = admin_ctrl.get_all_info_by_clinic_id(clinic_id=str(clinic_view.clinicid))
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/admin/membership/add_member', response_model=AddMemberResponse, tags=["admin"])
async def add_member_in_subscription(add_view: AddMemberRequest, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = membr_ctrl.add_member_in_plan(add_request=add_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return AddMemberResponse(userid=add_view.userid, planid=add_view.planid, members=resp)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/user/membership/add_member', response_model=AddMemberResponse, tags=["user"])
async def add_member_in_subscription_by_user(add_view_user: AddMemberRequestByUser,
                                             token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    add_view = AddMemberRequest(userid=userid, planid=add_view_user.planid, members=add_view_user.members)
    resp, msg = membr_ctrl.add_member_in_plan(add_request=add_view)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return AddMemberResponse(userid=add_view.userid, planid=add_view.planid, members=resp)


@app.post('/doctor/search_patients', tags=["doctor"])
async def search_patients(request_body: SearchPatientsByName, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        resp = doctor_ctrl.search_patients_by_name(request_body=SearchPatientsByName(
            doctor_id=doctorid,
            patient_name=request_body.patient_name
        ))
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/admin/search_patients', tags=["admin"])
async def search_patients_by_admin(request_body: SearchPatientsByName, token: str = Depends(oauth2_scheme_admin)):
    adminid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid=adminid)
    if admin_check is not None:
        resp, msg = doctor_ctrl.search_patients_by_name(request_body=request_body)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/doctor/get_available/virtual_slots', tags=["prescription"])
async def get_doctors_virtual_aval_slot_for_appointment_booking(request_data: SearchSlotsOnADate,
                                                                token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=request_data.doctor_id if request_data.doctor_id else doctorid)
    if doctor_check is not None:
        resp, msg = doctor_ctrl.get_virtual_availability_slots_for_a_date(
            request_data=request_data, doctor_id=request_data.doctor_id if request_data.doctor_id else doctorid)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/doctor/clinic_doctor_availability', tags=["doctor"])
async def doctor_get_all_mapped_available_slots(doctor_search_view: DoctorClinicAvailableSlot,
                                                token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    doctor_check = doctor_ctrl.get_doctor_by_id(doctorid=doctorid)
    if doctor_check is not None:
        search_view = CheckDoctorAvailableSlot(doctorid=doctorid, clinicid=doctor_search_view.clinicid,
                                               search_date=doctor_search_view.search_date)
        resp, msg = ptn_ctrl.get_clinic_doctor_availability(search_view=search_view)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.get('/chat/support/get_support_id', tags=["Chat"])
async def get_admin_id_for_support_chat(token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    userid = get_valid_doctor_from_token(token=token)
    if userid is None:
        raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = chat_ctrl.get_ayoo_support_id()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/chat/get', tags=["Chat"])
async def get_chat(chat_data: GetChat, token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    userid = get_valid_doctor_from_token(token=token)
    if userid is None:
        raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = chat_ctrl.get_chat_by_id(chat_data=chat_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/chat/close', tags=["Chat"])
async def close_chat(chat_data: CloseChat, token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    userid = get_valid_doctor_from_token(token=token)
    if userid is None:
        raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = chat_ctrl.close_chat(chat_data=chat_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/get_user_all_chats', tags=["Chat"])
async def get_all_chats_of_user(chat_data: GetUsersAllChat,
                                token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    userid = get_valid_doctor_from_token(token=token)
    if userid is None:
        raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = chat_ctrl.get_all_chats_of_user(chat_data=chat_data)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/broadcast/msg', tags=["Chat"])
async def broadcast_msg_by_admin(chat_data: BroadcastMessage, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is None:
        raise HTTPException(status_code=409, detail='Invalid Admin')

    resp = chat_ctrl.broadcast_message(chat_data=chat_data, admin_id=admin_id)
    # if not resp:
    #     raise HTTPException(status_code=409, detail=msg)
    # else:
    return resp


@app.post('/admin/specialization/add', tags=["admin"])
async def add_new_specialization(metadata: Specialization, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.specialization_add(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/specialization/update', tags=["admin"])
async def update_specialization(metadata: SpecializationUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.update_specialization_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/specialization/delete', tags=["admin"])
async def delete_specialization(metadata: SpecializationDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.delete_specialization_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/specialization/get', tags=["admin"])
async def get_specialists_and_specializations(metadata: SpecializationGet):
    # async def get_specialists_and_specializations(metadata: SpecializationGet,
    #                                                   token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):
    # userid = get_valid_doctor_from_token(token=token)
    # if userid is None:
    #     raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = metadata_ctrl.get_all_specializations_by_field(metadata=metadata)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/admin/practice_area/add', tags=["admin"])
async def add_new_practice_area(metadata: PracticeArea, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.practice_area_add(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/practice_area/update', tags=["admin"])
async def update_practice_area(metadata: PracticeAreaUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.update_practice_area_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/practice_area/delete', tags=["admin"])
async def delete_practice_area(metadata: PracticeAreaDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.delete_practice_area_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/practice_area/get', tags=["admin"])
async def get_practice_areas(metadata: PracticeAreaGet):
    # async def get_practice_areas(metadata: PracticeAreaGet, token: str = Depends(oauth2_scheme_doctor or oauth2_scheme)):

    # userid = get_valid_doctor_from_token(token=token)
    # if userid is None:
    #     raise HTTPException(status_code=409, detail='Invalid Token')

    resp, msg = metadata_ctrl.get_all_practice_areas(metadata=metadata)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        # logger.info(resp)
        return resp


@app.post('/admin/forms/add_new', tags=["admin-forms"])
async def add_new_form(metadata: FormAdd, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.add_new_form(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/forms/get', tags=["admin-forms"])
async def get_all_forms(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    if admin_id is not None:
        resp, msg = metadata_ctrl.get_all_forms()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/forms/update', tags=["admin-forms"])
async def update_form(metadata: FormUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.update_form_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/forms/delete', tags=["admin-forms"])
async def delete_form(metadata: FormDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.delete_form_by_id(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question/add_new', response_model=QuestionResponse, tags=["admin-forms"])
async def add_question_to_form(metadata: QuestionAdd, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.add_new_question_to_form(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return QuestionResponse(question_id=resp.question_id, form_id=resp.form_id,
                                    question_type=resp.question_type, question_text=resp.question_text,
                                    option1=resp.option1, option1_point=resp.option1_point, option2=resp.option2,
                                    option2_point=resp.option2_point, option3=resp.option3,
                                    option3_point=resp.option3_point, option4=resp.option4,
                                    option4_point=resp.option4_point, option5=resp.option5,
                                    option5_point=resp.option5_point, option6=resp.option6,
                                    option6_point=resp.option6_point, option7=resp.option7,
                                    option7_point=resp.option7_point, option8=resp.option8,
                                    option8_point=resp.option8_point, option9=resp.option9,
                                    option9_point=resp.option9_point, option10=resp.option10,
                                    option10_point=resp.option10_point)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/question/get', tags=["admin-forms"])
async def get_questions_of_form(metadata: GetFormQuestions, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    # admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_id is not None:
        resp, msg = metadata_ctrl.get_all_form_questions(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question/update', response_model=QuestionResponse, tags=["admin-forms"])
async def update_form_questions(metadata: QuestionUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.update_form_question(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return QuestionResponse(question_id=metadata.question_id, form_id=resp.form_id,
                                    question_type=resp.question_type, question_text=resp.question_text,
                                    option1=resp.option1, option1_point=resp.option1_point, option2=resp.option2,
                                    option2_point=resp.option2_point, option3=resp.option3,
                                    option3_point=resp.option3_point, option4=resp.option4,
                                    option4_point=resp.option4_point, option5=resp.option5,
                                    option5_point=resp.option5_point, option6=resp.option6,
                                    option6_point=resp.option6_point, option7=resp.option7,
                                    option7_point=resp.option7_point, option8=resp.option8,
                                    option8_point=resp.option8_point, option9=resp.option9,
                                    option9_point=resp.option9_point, option10=resp.option10,
                                    option10_point=resp.option10_point)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question/delete', tags=["admin-forms"])
async def delete_form_questions(metadata: QuestionDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.delete_form_question(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question_response/add_new', tags=["admin-forms"])
async def add_question_response_to_form(metadata: QuestionResponseRangeAdd, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.add_new_question_response_to_form(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return dict(form_id=resp.form_id,
                        range1_lower_limit=resp.range1_lower_limit,
                        range1_upper_limit=resp.range1_upper_limit,
                        range1_response_text=resp.range1_response_text,
                        range2_lower_limit=resp.range2_lower_limit,
                        range2_upper_limit=resp.range2_upper_limit,
                        range2_response_text=resp.range2_response_text,
                        range3_lower_limit=resp.range3_lower_limit,
                        range3_upper_limit=resp.range3_upper_limit,
                        range3_response_text=resp.range3_response_text,
                        range4_lower_limit=resp.range4_lower_limit,
                        range4_upper_limit=resp.range4_upper_limit,
                        range4_response_text=resp.range4_response_text,
                        range5_lower_limit=resp.range5_lower_limit,
                        range5_upper_limit=resp.range5_upper_limit,
                        range5_response_text=resp.range5_response_text,
                        range6_lower_limit=resp.range6_lower_limit,
                        range6_upper_limit=resp.range6_upper_limit,
                        range6_response_text=resp.range6_response_text
                        )
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question_response/update', tags=["admin-forms"])
async def update_question_response(metadata: QuestionResponseRangeUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.update_question_response(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question_response/delete', tags=["admin-forms"])
async def delete_form_question_response(metadata: QuestionResponseDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.delete_question_response(metadata=metadata)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/forms/bulk_sample/download', tags=["admin-forms"])
async def sample_bulk_form_format_download(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.download_sample_form_format()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/forms/add_new_forms/excel', tags=["admin-forms"])
async def upload_form_in_bulk(file: UploadFile = File(...), token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.add_new_form_by_excel(metadata=file)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/questions/bulk_sample/download', tags=["admin-forms"])
async def sample_bulk_question_format_download(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.download_sample_question_format()
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/question/add_new/excel', tags=["admin-forms"])
async def upload_question_in_bulk(form_id: str = Query(None), file: UploadFile = File(...),
                                  token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp, msg = metadata_ctrl.add_questions_by_excel(form_id=form_id, metadata=file)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post("/user/get_all_cases", tags=["user"])
async def get_all_user_cases(patient: PatientId, token: str = Depends(oauth2_scheme_admin)):
    logged_in_user_id = get_valid_admin_from_token(token=token)
    if logged_in_user_id is not None:
        resp, msg = psychiatry_ctrl.get_user_cases(logged_in_user=logged_in_user_id, user_id=patient.patient_id)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/welcome-msg', tags=["Chat"])
async def welcome_msg_to_old_users(token: str = Depends(oauth2_scheme_doctor)):
    userid = get_valid_doctor_from_token(token=token)
    resp, msg = chat_ctrl.welcome_msg_to_old_users(admin_id=userid)
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/mental_health_case_details/get', tags=["Mental Health"])
async def get_mental_health_case_details(case_data: GetMentalHealthCaseDetails,
                                         token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_mental_health_case_details(data=case_data)
        if not resp:
            raise HTTPException(status_code=409, detail='No data found')
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/mental_health_case_history/get', tags=["Mental Health"])
async def get_mental_health_case_history(case_data: GetMentalHealthCaseDetails,
                                         token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_mental_health_case_history(data=case_data)
        if not resp:
            raise HTTPException(status_code=409, detail='No data found')
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/psychiatry/chief_complain/delete1', tags=["Mental Health"])
async def delete_one_element_from_psychiatry_chief_complain(chief_complain: DeleteChiefComplain,
                                                            token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/chief_complain/delete1 : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_psychiatry_chief_complain_single_entity(data=chief_complain,
                                                                              logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/chief_complain/delete1 : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/chief_complain/delete1 : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/chief_complain/delete1 : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/chief_complain/delete1', tags=["Mental Health"])
async def delete_one_element_from_therapy_chief_complain(chief_complain: DeleteChiefComplain,
                                                         token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/chief_complain/delete1 : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_therapy_chief_complain_single_entity(data=chief_complain, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/chief_complain/delete1 : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/chief_complain/delete1 : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/chief_complain/delete1 : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/chief_complain/add', tags=["Mental Health"])
async def add_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/chief_complain/add : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.add_chief_complain(data=chief_complain, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/chief_complain/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/chief_complain/add : status_code=200")

        return resp
    else:
        loggers['logger4'].info("/psychiatry/chief_complain/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/chief_complain/update', tags=["Mental Health"])
async def update_latest_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/chief_complain/update : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_latest_chief_complain(data=chief_complain,
                                                            logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/chief_complain/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/chief_complain/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/chief_complain/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/chief_complain/delete', tags=["Mental Health"])
async def delete_latest_chief_complain(chief_complain: GetMentalHealthCaseDetails,
                                       token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/chief_complain/delete : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_latest_chief_complain(data=chief_complain,
                                                            logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/chief_complain/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/chief_complain/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/chief_complain/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/doctor_notes/add', tags=["Mental Health"])
async def add_doctor_notes(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/doctor_notes/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.add_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/doctor_notes/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/doctor_notes/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/doctor_notes/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/doctor_notes/update', tags=["Mental Health"])
async def update_last_doctor_notes(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/doctor_notes/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/doctor_notes/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/doctor_notes/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/doctor_notes/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/doctor_notes/delete', tags=["Mental Health"])
async def delete_last_doctor_notes(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/doctor_notes/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/doctor_notes/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/doctor_notes/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/doctor_notes/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/sleep_and_appetite/add', tags=["Mental Health"])
async def add_sleep_and_appetite(data: SleepAndAppetite, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/sleep_and_appetite/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_sleep_and_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/sleep_and_appetite/update', tags=["Mental Health"])
async def update_last_sleep_and_appetite(data: SleepAndAppetite, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/sleep_and_appetite/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_sleep_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/sleep_and_appetite/delete', tags=["Mental Health"])
async def delete_last_sleep_and_appetite(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/sleep_and_appetite/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_sleep_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/sleep_and_appetite/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/substance_use_abuse/add', tags=["Mental Health"])
async def add_substance_use_abuse(data: SubstanceUseAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/substance_use_abuse/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/substance_use_abuse/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/substance_use_abuse/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/substance_use_abuse/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/substance_use_abuse/update', tags=["Mental Health"])
async def update_last_substance_use_abuse(data: SubstanceUseAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/substance_use_abuse/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/substance_use_abuse/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/substance_use_abuse/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/substance_use_abuse/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/substance_use_abuse/delete', tags=["Mental Health"])
async def delete_last_substance_use_abuse(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/substance_use_abuse/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/substance_use_abuse/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/substance_use_abuse/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/substance_use_abuse/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/assessment/add', tags=["Mental Health"])
async def add_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/assessment/update', tags=["Mental Health"])
async def update_last_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/assessment/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/assessment/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/assessment/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/assessment/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/assessment/delete', tags=["Mental Health"])
async def delete_last_assessment(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/assessment/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/assessment/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/assessment/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/assessment/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/lab_tests/add', tags=["Mental Health"])
async def add_lab_tests(data: LabTest, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/lab_tests/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/lab_tests/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/lab_tests/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/lab_tests/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/lab_tests/update', tags=["Mental Health"])
async def update_last_lab_tests(data: LabTest, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/lab_tests/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/lab_tests/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/lab_tests/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/lab_tests/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/lab_tests/delete', tags=["Mental Health"])
async def delete_last_lab_tests(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/lab_tests/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/lab_tests/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/lab_tests/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/lab_tests/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/case/appointment', tags=["Mental Health"])
async def psychiatrist_patient_appointment(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/case/appointment : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.list_appointments(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/case/appointment : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/case/appointment : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/case/appointment : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/medications/add', tags=["Mental Health"])
async def add_medications(data: MentalHealthMedication, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/medications/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/medications/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/medications/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/medications/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/medications/update', tags=["Mental Health"])
async def update_last_medications(data: MentalHealthMedication, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/medications/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/medications/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/medications/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/medications/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/medications/delete', tags=["Mental Health"])
async def delete_last_medications(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/medications/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/medications/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/medications/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/medications/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/treatment_plan/add', tags=["Mental Health"])
async def add_treatment_plan(data: TreatmentPlanAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/treatment_plan/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/treatment_plan/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/treatment_plan/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/treatment_plan/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/treatment_plan/update', tags=["Mental Health"])
async def update_last_treatment_plan(data: TreatmentPlanAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/treatment_plan/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        loggers['logger4'].info("/psychiatry/treatment_plan/update : status_code=200")
        resp = psychiatry_ctrl.update_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/treatment_plan/update : Response : " + str(dict(res=resp)))
        return resp
    else:
        loggers['logger4'].info("/psychiatry/treatment_plan/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/treatment_plan/delete', tags=["Mental Health"])
async def delete_last_treatment_plan(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/treatment_plan/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/treatment_plan/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/treatment_plan/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/treatment_plan/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/therapy_recommendation/add', tags=["Mental Health"])
async def add_therapy_recommendation(data: TherapyRecommendationAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/therapy_recommendation/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/therapy_recommendation/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/therapy_recommendation/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/therapy_recommendation/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/therapy_recommendation/update', tags=["Mental Health"])
async def update_last_therapy_recommendation(data: TherapyRecommendationAdd,
                                             token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/therapy_recommendation/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/therapy_recommendation/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/therapy_recommendation/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/psychiatry/therapy_recommendation/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/therapy_recommendation/delete', tags=["Mental Health"])
async def delete_last_therapy_recommendation(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/therapy_recommendation/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/therapy_recommendation/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/therapy_recommendation/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/psychiatry/therapy_recommendation/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/follow_up/add', tags=["Mental Health"])
async def add_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/follow_up/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/follow_up/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/follow_up/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/follow_up/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/follow_up/update', tags=["Mental Health"])
async def update_last_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/follow_up/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/follow_up/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/follow_up/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/follow_up/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/follow_up/delete', tags=["Mental Health"])
async def delete_last_follow_up(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/follow_up/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/follow_up/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/follow_up/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/follow_up/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/client_education/add', tags=["Mental Health"])
async def add_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/client_education/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.add_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/client_education/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/client_education/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/client_education/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/client_education/update', tags=["Mental Health"])
async def update_last_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/client_education/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/client_education/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/client_education/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/client_education/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/client_education/delete', tags=["Mental Health"])
async def delete_last_client_education(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/client_education/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/client_education/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/client_education/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/client_education/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/care_assessment/add', tags=["Mental Health"])
async def care_assessment_add(data: CareAssessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/care_assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.add_care_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/care_assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/care_assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/care_assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/care_assessment/remove_attribute', tags=["Mental Health"])
async def care_assessment_remove_attribute(data: CareAssessmentRemoveAttribute,
                                           token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/care_assessment/remove_attribute : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.remove_care_assessment_attribute(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/care_assessment/remove_attribute : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/care_assessment/remove_attribute : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/psychiatry/care_assessment/remove_attribute : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/close_case', tags=["Mental Health"])
async def add_case_summary(data: CaseSummary, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/close_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.record_case_summary(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/close_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/close_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/close_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/prescription_submit', tags=["Mental Health"])
async def psychiatrist_prescription_submit(data: CaseElement1, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/psychiatry/prescription_submit : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.psychiatrist_prescription_submit(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/psychiatry/prescription_submit : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/psychiatry/prescription_submit : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/psychiatry/prescription_submit : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/psychiatry/medicine_notification_toggle', tags=["Mental Health"])
async def mh_med_notif_toggle_switch(request_data: CaseElement, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = psychiatry_ctrl.med_notif_toggle_switch(data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/get_doctors_all_cases', tags=["Mental Health"])
async def mh_get_doctors_all_cases(request_data: GetDoctorCases, page_number: int = 1, page_size: int = 15,
                                   token: str = Depends(oauth2_scheme)):
    loggers['logger4'].info("/get_doctors_all_cases : request :" + str(dict(request_data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_doctors_all_cases(data=request_data, logged_in_user=user_id, page_number=page_number,
                                                     page_size=page_size)
        loggers['logger4'].info("/get_doctors_all_cases : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/get_doctors_all_cases : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/get_doctors_all_cases : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/doctor_case_info', tags=["Mental Health"])
async def get_doctors_single_case_info(request_data: GetDoctorCaseInfo, token: str = Depends(oauth2_scheme)):
    loggers['logger4'].info("/doctor_case_info : request :" + str(dict(request_data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.doctor_case_info(data=request_data, logged_in_user=user_id)
        loggers['logger4'].info("/doctor_case_info : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/doctor_case_info : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/doctor_case_info : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/chief_complain/add', tags=["Mental Health"])
async def add_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/chief_complain/add : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.add_therapy_chief_complain(data=chief_complain, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/chief_complain/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/chief_complain/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/chief_complain/adds : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/chief_complain/update', tags=["Mental Health"])
async def update_latest_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/chief_complain/update : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_therapy_chief_complain(data=chief_complain,
                                                          logged_in_user=user_id)
        loggers['logger4'].info("/therapy/chief_complain/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/chief_complain/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/chief_complain/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/chief_complain/delete', tags=["Mental Health"])
async def delete_latest_chief_complain(chief_complain: GetMentalHealthCaseDetails,
                                       token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/chief_complain/delete : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_therapy_chief_complain(data=chief_complain,
                                                          logged_in_user=user_id)
        loggers['logger4'].info("/therapy/chief_complain/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/chief_complain/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/chief_complain/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/sleep_and_appetite/add', tags=["Mental Health"])
async def add_sleep_and_appetite(data: SleepAndAppetite, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/sleep_and_appetite/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_sleep_and_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/sleep_and_appetite/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/sleep_and_appetite/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/sleep_and_appetite/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/sleep_and_appetite/update', tags=["Mental Health"])
async def update_last_sleep_and_appetite(data: SleepAndAppetite, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/sleep_and_appetite/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_sleep_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/sleep_and_appetite/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/sleep_and_appetite/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/sleep_and_appetite/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/sleep_and_appetite/delete', tags=["Mental Health"])
async def delete_last_sleep_and_appetite(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/sleep_and_appetite/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_sleep_appetite(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/sleep_and_appetite/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/sleep_and_appetite/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/sleep_and_appetite/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes/add', tags=["Mental Health"])
async def add_notes(data: TherapyNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes/update', tags=["Mental Health"])
async def update_last_notes(data: TherapyNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes/delete', tags=["Mental Health"])
async def delete_last_notes(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/assessment/add', tags=["Mental Health"])
async def add_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/assessment/update', tags=["Mental Health"])
async def update_last_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/assessment/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/assessment/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/assessment/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/assessment/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/assessment/delete', tags=["Mental Health"])
async def delete_last_assessment(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/assessment/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/assessment/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/assessment/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/assessment/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/treatment_plan/add', tags=["Mental Health"])
async def add_treatment_plan(data: TherapyTreatmentPlan, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/treatment_plan/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/treatment_plan/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/treatment_plan/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/treatment_plan/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/treatment_plan/update', tags=["Mental Health"])
async def update_last_treatment_plan(data: TherapyTreatmentPlan, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/treatment_plan/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/treatment_plan/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/treatment_plan/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/treatment_plan/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


# @app.post('/therapy/treatment_plan/comment/update', tags=["Mental Health"])
# async def update_last_treatment_plan(data: TherapyTreatmentPlanComment, token: str = Depends(oauth2_scheme_doctor)):
#     user_id = get_valid_doctor_from_token(token=token)
#     if user_id is not None:
#         resp = therapy_ctrl.update_treatment_plan_comment(data=data, logged_in_user=user_id)
#         return resp
#     else:
#         raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/treatment_plan/delete', tags=["Mental Health"])
async def delete_last_treatment_plan(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/treatment_plan/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_treatment_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/treatment_plan/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/treatment_plan/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/treatment_plan/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


# @app.post('/therapy/plan_response/add', tags=["Mental Health"])
# async def add_plan_response(data: TreatmentPlanResponse, token: str = Depends(oauth2_scheme_doctor)):
#     user_id = get_valid_doctor_from_token(token=token)
#     if user_id is not None:
#         resp = therapy_ctrl.record_treatment_plan_response(data=data, logged_in_user=user_id)
#         return resp
#     else:
#         raise HTTPException(status_code=409, detail='Invalid User Token')
#
#
# @app.post('/therapy/plan_response/update', tags=["Mental Health"])
# async def update_last_plan_response(data: TreatmentPlanResponse, token: str = Depends(oauth2_scheme_doctor)):
#     user_id = get_valid_doctor_from_token(token=token)
#     if user_id is not None:
#         resp = therapy_ctrl.update_treatment_plan_response(data=data, logged_in_user=user_id)
#         return resp
#     else:
#         raise HTTPException(status_code=409, detail='Invalid User Token')
#
#
# @app.post('/therapy/plan_response/delete', tags=["Mental Health"])
# async def delete_last_plan_response(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
#     user_id = get_valid_doctor_from_token(token=token)
#     if user_id is not None:
#         resp = therapy_ctrl.delete_treatment_plan_response(data=data, logged_in_user=user_id)
#         return resp
#     else:
#         raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/care_assessment/add', tags=["Mental Health"])
async def add_care_assessment(data: CareAssessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/care_assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.add_care_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/care_assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/care_assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/care_assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/care_assessment/remove_attribute', tags=["Mental Health"])
async def care_assessment_remove_attribute(data: CareAssessmentRemoveAttribute,
                                           token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/care_assessment/remove_attribute : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.remove_care_assessment_attribute(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/care_assessment/remove_attribute : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/care_assessment/remove_attribute : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/therapy/care_assessment/remove_attribute : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/therapy_recommendation/add', tags=["Mental Health"])
async def add_therapy_recommendation(data: TherapyRecommendationAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/therapy_recommendation/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/therapy_recommendation/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/therapy_recommendation/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/therapy_recommendation/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/therapy_recommendation/update', tags=["Mental Health"])
async def update_last_therapy_recommendation(data: TherapyRecommendationAdd,
                                             token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/therapy_recommendation/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/therapy_recommendation/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/therapy_recommendation/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/therapy_recommendation/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/therapy_recommendation/delete', tags=["Mental Health"])
async def delete_last_therapy_recommendation(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/therapy_recommendation/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_therapy_recommendation(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/therapy_recommendation/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/therapy_recommendation/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/therapy_recommendation/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/follow_up/add', tags=["Mental Health"])
async def add_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/follow_up/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/follow_up/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/follow_up/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/follow_up/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/follow_up/update', tags=["Mental Health"])
async def update_last_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/follow_up/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/follow_up/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/follow_up/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/follow_up/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/follow_up/delete', tags=["Mental Health"])
async def delete_last_follow_up(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/follow_up/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/follow_up/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/follow_up/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/follow_up/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes_for_doctor/add', tags=["Mental Health"])
async def add_notes_for_doctor(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes_for_doctor/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.add_notes_for_doctor(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes_for_doctor/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes_for_doctor/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes_for_doctor/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes_for_doctor/update', tags=["Mental Health"])
async def update_last_notes_for_doctor(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes_for_doctor/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_notes_for_doctor(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes_for_doctor/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes_for_doctor/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes_for_doctor/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/notes_for_doctor/delete', tags=["Mental Health"])
async def delete_last_notes_for_doctor(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/notes_for_doctor/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_notes_for_doctor(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/notes_for_doctor/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/notes_for_doctor/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/notes_for_doctor/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/client_education/add', tags=["Mental Health"])
async def add_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/client_education/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.add_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/client_education/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/client_education/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/client_education/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/client_education/update', tags=["Mental Health"])
async def update_last_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/client_education/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/client_education/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/client_education/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/client_education/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/client_education/delete', tags=["Mental Health"])
async def delete_last_client_education(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/client_education/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/client_education/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/client_education/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/client_education/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/close_case', tags=["Mental Health"])
async def add_case_summary(data: CaseSummary, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/close_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.record_case_summary(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/close_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/close_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/close_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/therapy/prescription_submit', tags=["Mental Health"])
async def psychiatrist_prescription_submit(data: CaseElement1, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/therapy/prescription_submit : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.therapist_prescription_submit(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/therapy/prescription_submit : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/therapy/prescription_submit : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/therapy/prescription_submit : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/mental_health/refer_case', tags=["Mental Health"])
async def refer_mental_health_case(data: ReferCase, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/mental_health/refer_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.refer_case(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/mental_health/refer_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/mental_health/refer_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/mental_health/refer_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/mental_health/update_referred_case', tags=["Mental Health"])
async def update_referred_case(data: ReferCaseUpdate, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/mental_health/update_referred_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.update_referred_case(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/mental_health/update_referred_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/mental_health/update_referred_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/mental_health/update_referred_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/mental_health/delete_referred_case', tags=["Mental Health"])
async def delete_referred_case(data: ReferCaseDelete, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/mental_health/delete_referred_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = therapy_ctrl.delete_referred_case(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/mental_health/delete_referred_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/mental_health/delete_referred_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/mental_health/delete_referred_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/all_practice_area/get_available/virtual_slots', tags=["prescription"])
async def get_doctors_virtual_aval_slot_for_appointment_booking():
    resp, msg = doctor_ctrl.get_first_available_slot_based_on_practice_area()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/all_doctors/name_and_ids', tags=["prescription"])
async def get_all_doctors():
    resp, msg = doctor_ctrl.all_doctors_name_and_ids()
    if not resp:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return resp


@app.post('/mental_health/care_assessment/patient_feedback', tags=["Mental Health"])
async def care_assessment_update_by_patient(data: CareAssessmentPatientFeedback, token: str = Depends(oauth2_scheme)):
    user_id = userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp = psychiatry_ctrl.care_assessment_patient_feedback(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/mental_health/assessment_questions/add', tags=["Mental Health"])
async def assessment_questions_add(data: AssessmentFormsSubmit, token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.add_form_assessment_results(data=data, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


# @app.websocket("/ayoo-chat")
# async def websocket_endpoint(websocket: WebSocket):
#     await websocket.accept()
#     while True:
#         data = await websocket.receive_text()
#         logger.info(data)
#         a = "some operations performed in db"
#         await websocket.send_text(f"Message received: {data}, extra response from backend-> {a}")
#

@app.websocket("/chat/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    await chat_manager.connect(websocket, user_id)

    try:
        while True:
            # data = await websocket.receive_text()
            # recipient_id, message = data.split(":", 1)
            data = await websocket.receive_json()
            recipient_id = data['recipient_id']
            message = data['message']
            attachment_encoded = data['attachment_encoded'] if 'attachment_encoded' in data else None

            # if recipient_id == "*":
            #     await chat_manager.broadcast(message=message,
            #                                  attachment_encoded=attachment_encoded)
            # else:
            await chat_manager.send_personal_message(chat_data=SaveChatModel(
                sender_id=user_id,
                recipient_id=recipient_id,
                message=message,
                attachment_encoded=attachment_encoded
            ))
    except WebSocketDisconnect:
        chat_manager.disconnect(user_id)
        # await chat_manager.broadcast(f"Client #{user_id} left the chat")
    # finally:
    #     chat_manager.disconnect(user_id)


@app.post('/mental_health/clinical_history/patient_permission', tags=["Mental Health"])
async def change_access_to_clinical_history(data: ClinicalHistoryPermission,
                                            token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    valid_user = ctrl.get_user_details(userid=user_id, mongo=mongodb_conn)
    if valid_user is not None:
        resp = psychiatry_ctrl.allow_permission_for_clinical_history(data=data, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/mental_health/clinical_history/get', tags=["Mental Health"])
async def get_clinical_history(data: GetClinicalHistory, token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_clinical_history(data=data, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.get('/admin/patient_clinical_history', tags=["Mental Health"])
async def get_clinical_history(patient_id: str, token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = medical_history_ctrl.get_clinical_history_from_admin(patient_id=patient_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/admin/get_patient_report_list', tags=["reports"])
async def get_all_patient_reports_list(data: GetReportsList, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_reports_list(data=data, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/patient/get_patient_report_list', tags=["reports"])
async def get_patient_report_list_prescribed_by_doctor(token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp = psychiatry_ctrl.get_patient_report_list_prescribed_by_doctor(logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/upload_lab_test_reports_for_a_case', tags=["reports"])
async def upload_report_prescribed_by_doctor(add_report: UploadPrescribedReport, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.upload_prescribed_report(data=add_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/upload_other_documents_for_a_case', tags=["reports"])
async def upload_report_prescribed_by_doctor(add_report: UploadExtraDocument, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.upload_unprescribed_reports_and_documents(data=add_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/update_uploaded_report', tags=["reports"])
async def update_an_uploaded_report(update_report: UpdatePrescribedReport, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_prescribed_report(data=update_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/update_uploaded_document', tags=["reports"])
async def update_an_uploaded_document(update_report: UpdateExtraDocument, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.update_unprescribed_reports_and_documents(data=update_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/delete_uploaded_report', tags=["reports"])
async def delete_an_uploaded_report(delete_report: DeleteReport, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_report(data=delete_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/delete_uploaded_document', tags=["reports"])
async def delete_an_uploaded_document(delete_report: DeleteDocument, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.delete_document(data=delete_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/get_all_uploaded_reports', tags=["reports"])
async def get_all_uploaded_reports_of_a_patient(get_report: GetPatientReports, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_all_reports_of_patient(data=get_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/get_all_uploaded_documents', tags=["reports"])
async def get_all_uploaded_documents_of_a_patient(get_report: GetPatientReports, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_all_documents_of_patient(data=get_report, logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/doctor/deactivate_account', tags=["doctor"])
async def deactivate_doctor_account(token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    if doctor_id is not None:
        resp = doctor_ctrl.deactivate_doctor_account(logged_in_user=doctor_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/user/deactivate_account', tags=["user"])
async def deactivate_user_account(token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp = ctrl.deactivate_user_account(logged_in_user=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/doctor/activate_account', tags=["doctor"])
async def activate_doctor_account(data: ActivateDoctorAccount, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)

    if admin_check is not None:
        resp = doctor_ctrl.activate_doctor_account(doctor_id=data.doctor_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/doctor/deactivate_account', tags=["doctor"])
async def activate_doctor_account1(data: ActivateDoctorAccount, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:

        resp = doctor_ctrl.deactivate_doctor_account1(doctor_id=data.doctor_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/user/activate_account', tags=["user"])
async def activate_user_account(data: ActivateUserAccount, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)

    if admin_check is not None:
        resp = ctrl.activate_user_account(logged_in_user=admin_id, user_email=data.user_email)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/faq', response_model=faqview, tags=["admin"])
async def add_faq(faq: faqview):  # , token: str = Depends(oauth2_scheme_admin)):
    return faq_cltr.faq_submit(faq_submitmodel=faq)


@app.get('/admin/faq_all', tags=["admin"])
async def view_faq():  # , token: str = Depends(oauth2_scheme_admin)):
    faq_resp = faq_cltr.faq_display()
    return faq_resp


@app.get('/user/faq_all', tags=["User"])
async def view_faq():  # , token: str = Depends(oauth2_scheme_admin)):
    faq_resp = faq_cltr.faq_display_user()
    return faq_resp


@app.post('/admin/faq_update', response_model=faqview, tags=["admin"])
async def faq_update(id: str, faq: faqview):  # , token: str = Depends(oauth2_scheme_admin)):
    return faq_cltr.faq_update(id=id, faq_submitmodel=faq)


@app.post('/admin/faq_delete', tags=["admin"])
async def faq_delete(id: str):  # , token: str = Depends(oauth2_scheme_admin)):
    return faq_cltr.faq_delete(id=id)


@app.get('/get/failed_appointment', tags=["payment"])
async def get_payment(user: appointment_failed,
                      token: str = Depends(oauth2_scheme_admin)):  # , token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        return rev_cltr.appointment_failure(user=user)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/get/payment', tags=["payment"])  # length , req_body
async def get_payment(data: Get_payment,
                      token: str = Depends(oauth2_scheme_admin)):  # , token: str = Depends(oauth2_scheme_admin)):
    loggers['logger3'].info("/get/payment : request :" + str(dict(data)))
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)

    if admin_check is not None:
        admin_role = admin_ctrl.get_admin_role(admin_id=userid)
        if admin_role is None:
            raise Exception('Invalid admin')
        exclude_id = None if admin_role == 'super_admin' else EXCLUDE_ID
        li = rev_cltr.payment_display(request_data=data, exclude_id=exclude_id)
        skip = 0
        if data.skip:
            skip = data.skip
        start_idx = skip
        limit = 30
        if data.limit:
            limit = data.limit
        end_idx = skip + limit
        paginated_items = li[start_idx:end_idx]
        loggers['logger3'].info("/get/payment : Response : ")
        for pg in paginated_items:
            dr = {}
            dr['order_id'] = pg['order_id']
            dr['payment_status'] = pg['payment_status']
            loggers['logger3'].info(str(dr))

        loggers['logger3'].info("/get/payment : status_code=200")
        res = dict(length=len(li), payment_list=paginated_items)
        return res
    else:
        loggers['logger3'].info("/get/payment : {status_code=409, detail= Invalid Admin")
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/invoice_receipt')
async def get_payment(invoice_id: str):
    return rev_cltr.appointment_payment(invoice_id=invoice_id)


@app.get('/get/payment_appointment_list', tags=["payment"])
async def get_payment(token: str = Depends(oauth2_scheme)):
    loggers['logger3'].info("/get/payment_appointment_list : request : User_Token")
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        Resp = rev_cltr.payment_list(user_id=user_id)
        loggers['logger3'].info("/get/payment_appointment_list : Response : " + str(list(Resp)))
        loggers['logger3'].info("/get/payment_appointment_list : status_code=200")
        return Resp
    else:
        loggers['logger3'].info("/get/payment_appointment_list : {status_code=409, detail= Invalid user")
        raise HTTPException(status_code=409, detail='Invalid user')


@app.get('/get/receipt', tags=["payment"])
async def get_receipt(info: Payment_info, token: str = Depends(oauth2_scheme)):
    loggers['logger3'].info("/get/receipt : request :" + str(dict(info)))
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        Resp = rev_cltr.get_receipt(info=info)
        loggers['logger3'].info("/get/receipt : Response : " + str(dict(Resp)))
        loggers['logger3'].info("/get/receipt : status_code=200")
        return Resp
    else:
        loggers['logger3'].info("/get/receipt : {status_code=409, detail= Invalid user")
        raise HTTPException(status_code=409, detail='Invalid user')


@app.post('/delete/payment', tags=["payment"])
async def delete_payment(id: str, token: str = Depends(oauth2_scheme_admin)):
    return rev_cltr.payment_delete(id=id)


@app.post('/payment/appointment', tags=["payment"])
async def appointment_payment(appointment: Payments_appointment):  #: str = Depends(oauth2_scheme_admin)):
    logger.info("/payment/appointment")
    loggers['logger3'].info("/payment/appointment : request :" + str(dict(appointment)))
    res = rev_cltr.payment_appointment(appointment=appointment)
    loggers['logger3'].info("/payment/appointment : Response : " + str(dict(order_id=res)))
    loggers['logger3'].info("/payment/appointment : status_code=200")
    return res


@app.get('/display_payment/info', tags=["payment"])
async def appointment_payment(appointment_id: str, token: str = Depends(oauth2_scheme_admin)):
    user_id = get_valid_admin_from_token(token=token)
    if user_id is not None:
        return rev_cltr.appointment_payment(appointment_id=appointment_id)
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


"""# Add content to the PDF
        output_pdf.setFont("Arial", 12)
        output_pdf.drawString(50, 700, "INVOICE: ")
        j=20
        for li in dic['list']:
# Access dictionary values and add them to the PDF
            output_pdf.drawString(50, 700-j, "TREATMENTS: Consultation")
            j+=20
            output_pdf.drawString(50, 700-j, "DESCRIPTION: {}".format(li['des']))
            j+=20
            output_pdf.drawString(50, 700-j, "UNIT COST: ₹{}".format(li['amount']))
            j+=20
            #output_pdf.drawString(50, 700-j, "Transaction ID: {}".format(li['Transaction_id']))
            #j+=20"""


# Save the PDF file


@app.post('/get/payment/status', tags=["payment"])
async def appointment_payment(
        paymentview: Paymentstatus):  #: str = Depends(oauth2_scheme_admin)):response = await asyncio.wait_for(api_response(url, timeout), timeout)
    loggers['logger3'].info(
        "/get/payment/status : request :" + str(dict(paymentview)))
    Resp = rev_cltr.payment_status(paymentview=paymentview)
    loggers['logger3'].info("/get/payment/status : Response : " + str(Resp))
    loggers['logger3'].info("/get/payment/status : status_code=200")
    return Resp


@app.post('/get/payment/status1', tags=["payment"])
async def appointment_payment(
        order_id: str):  #: str = Depends(oauth2_scheme_admin)):response = await asyncio.wait_for(api_response(url, timeout), timeout)
    # paymentview= {"order_id":order_id}
    loggers['logger3'].info(
        "/get/payment/status1 : request :" + str(dict(orderid=order_id)))
    Resp = rev_cltr.payment_status(
        paymentview=Paymentstatus(order_id=order_id))
    loggers['logger3'].info("/get/payment/status1 : Response : " + str(Resp))
    loggers['logger3'].info("/get/payment/status1 : status_code=200")
    return Resp


@app.get('/.well-known/assetlinks.json')
async def assetlinks():  #: str = Depends(oauth2_scheme_admin)):response = await asyncio.wait_for(api_response(url, timeout), timeout)
    return [
        {
            "relation": [
                "delegate_permission/common.handle_all_urls"
            ],
            "target": {
                "namespace": "android_app",
                "package_name": "com.ayoo.care",
                "sha256_cert_fingerlogger.infos": [
                    "33:13:7D:B5:7D:41:19:BE:A0:9A:25:A6:FC:76:2C:20:76:13:0D:66:5F:87:BE:30:02:2C:12:22:FA:FF:92:BE"
                ]
            }
        }
    ]


@app.get('/.well-known/apple-app-site-association')
#: str = Depends(oauth2_scheme_admin)):response = await asyncio.wait_for(api_response(url, timeout), timeout)
async def apple_app_site_association():
    return {
        "applinks": {
            "apps": [],
            "details": [
                {
                    "appIDs": ["**********.com.ayoo.patients"],
                    "paths": ["*"]
                }
            ]
        }

    }


"""
import aiohttp
import asyncio

async def api_response(url: str, timeout: int) -> str:
    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
        try:
            async with session.get(url) as response:
                logger.info("api_response !!!")
                logger.info(response)
                data = await response.text()
                #logger.info(data)
                return data
        except asyncio.TimeoutError:
            # Handle timeout error
            return "Timeout occurred during the API request" """

"""
@app.websocket("/get/payment/status1/{order_id}")
async def websocket_endpoint1(websocket: WebSocket, order_id: str):
    await websocket.accept()
    try:
        # Set the timeout duration (5 minutes = 300 seconds)
        timeout = 15000
        data = await websocket.receive_json()
        url= data['url']
        logger.info(url)
        # Specify the URL to make the HTTP request
        #url = url

        # Call the API and wait for the response
        response =await asyncio.wait_for(api_response(url, timeout), timeout)




        if response:
            await asyncio.sleep(5)
            paymentview = dict(order_id=order_id)
            return rev_cltr.payment_status(paymentview=paymentview)

    except asyncio.TimeoutError:
        # Handle timeout error
        await websocket.send_text("Payment API timeout")

    finally:
        await websocket.close() """

##########################################################################

merchant_id = CCAVENUE_MERCHANT_ID
access_code = CCAVENUE_ACCESS_CODE
working_key = CCAVENUE_WORKING_KEY


def pad(data):
    length = 16 - (len(data) % 16)
    data += chr(length) * length
    return data


def encrypt(plainText, workingKey):
    iv = bytes(
        '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
    plainText = pad(plainText)
    res = bytes(plainText, 'utf-8')
    encDigest = hashlib.md5(workingKey.encode('utf-8'))
    logger.info(encDigest.digest())
    logger.info(type(encDigest.digest()))
    logger.info("step 1")
    # encDigest.update(workingKey.encode())
    enc_cipher = AES.new(encDigest.digest(), AES.MODE_CBC, iv)
    logger.info("step 2")
    encryptedText = enc_cipher.encrypt(res).hex()
    logger.info("step 3")
    # tr = encryptedText.encode('hex')
    # return tr
    return encryptedText


def validate_ccavenue(order_id, workingKey):
    data = 'order_no=' + order_id
    # data={'order_id':order_id}
    enc = encrypt(data, workingKey)

    # enc_request=63957FB55DD6E7B968A7588763E08B240878046EF2F520C44BBC63FB9CCE726209A4734877F5904445591304ABB2F5E598B951E39EAFB9A24584B00590ADB077ADE5E8C444EAC5A250B1EA96F68D22E44EA2515401C2CD753DBA91BD0E7DFE7341BE1E7B7550&access_code=8JXENNSSBEZCU8KQ&command=confirmOrder&request_type=XML&response_type=XML&version=1.2
    # url = "https://test.ccavenue.com/transaction/transaction.do?enc_request="+enc+"&access_code="+access_code+"&request_type=STRING&response_type=STRING&command=orderStatusTracker&order_no="+order_id
    url = "https://test.ccavenue.com/transaction/transaction.do?command=orderStatusTracker&encRequest=" + \
          enc + "&access_code=" + access_code + "&order_no=" + order_id
    response = requests.get(url)

    iv = bytes(
        '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
    encDigest = hashlib.md5(workingKey.encode('utf-8')).digest()
    dec_cipher = AES.new(encDigest, AES.MODE_CBC, iv)
    hex_response = response.content.hex()
    decryptedText = dec_cipher.decrypt(bytes.fromhex(hex_response))
    # decryptedText = dec_cipher.decrypt(cipherText).hex()
    logger.info("decryptedText of status !!!!!!!!!!!!!")
    logger.info(decryptedText)
    # text = decryptedText.rstrip(b'\0').decode('utf-8')

    return


@app.post('/get/status')
async def handle_webhook(order_id: str = Form(...), encResp: str = Form(...)):
    logger.info("Handling webhook request")
    logger.info(encResp)
    decrypt_response = rev_cltr.decrypt(encResp, working_key)
    return decrypt_response


def payment_status_test(status):
    base_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{page_title}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }}
        .container {{
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }}
        .success-icon {{
            color: #4CAF50;
            font-size: 100px;
        }}
        .failure-icon {{
            color: #FF0000;
            font-size: 100px;
        }}
        h1 {{
            color: #333;
        }}
        p {{
            color: #666;
        }}
        @media screen and (max-width: 600px) {{
            .container {{
                margin: 20px 10px;
            }}
        }}
    </style>
</head>
    <body>
        <div class="container">
            <div class="{icon_class}-icon">{icon_html}</div>
            <h1>{title}</h1>
            <p>{message}</p>
        </div>
    </body>
    </html>
    """
    print(status)
    if status == "success":
        content = base_html.format(
            icon_class="success",
            icon_html="&#10004;",
            title="Payment Successful!",
            page_title="Payment Successful",
            message="Thank you for your purchase. Your transaction has been completed successfully."
        )
    else:
        content = base_html.format(
            icon_class="failure",
            icon_html="&#10008;",
            title="Payment Failed",
            page_title="Payment Failed",
            message="Sorry, your transaction could not be completed. Please try again or contact support."
        )

    return content


@app.post("/test/", response_class=HTMLResponse, include_in_schema=False)
async def ccavResponseHandler():
    content = payment_status_test("success")
    return HTMLResponse(content)


# POINTER: Normal Payment response
@app.post("/ccavResponseHandler/", response_class=HTMLResponse, include_in_schema=False)
async def ccavResponseHandler(secret_key: str, request: Request):
    form_data = await request.form()
    logger.info("Handling ccavenue reposnse")
    loggers['logger3'].info("/ccavResponseHandler/ : request :" + str(dict(data=request)))
    # Specify the URL to make the HTTP request

    # Call the API and wait for the response
    logger.info("Response from ccavenue!!!!!!!!!!!!!!!!!!!!")
    logger.info(form_data['encResp'])
    logger.info("Secret Key!!!!!")
    logger.info(secret_key)
    status = rev_cltr.decrypt(form_data['encResp'], working_key, secret_key)
    loggers['logger3'].info("/ccavResponseHandler/ : Response : status=" + str(status))
    content = payment_status_test(status)
    return HTMLResponse(content=content)
    # plainText = res(request.form['encResp'])
    # return encResp


@app.post("/ccavRequestHandler/")  # , response_class=HTMLResponse)
async def login(paymentview: Paymentview):
    logger.info("/ccavRequestHandler/")
    loggers['logger3'].info("/ccavRequestHandler/ : request :" + str(dict(paymentview)))
    if paymentview.appointment_id == '':
        loggers['logger3'].info("/ccavRequestHandler/ : response : status_code=409,detail=blank appointment id")
        raise HTTPException(status_code=409, detail="blank appointment id provided")
    secret_key = str(uuid.uuid4())
    p_merchant_id = merchant_id
    p_order_id = str(uuid.uuid4())
    logger.info("order ID !!!!!!!!!!!!!")
    logger.info(p_order_id)
    # logger.info(redirect_url_fetch[1:])
    logger.info(cancel_url_fetch)
    logger.info("Handling CCavenue request")

    p_currency = "INR"
    p_amount = paymentview.amount
    passwd = encrypt_password(secret_key)
    p_redirect_url = f"{redirect_url_fetch[1:]}?secret_key={passwd}".replace('"', '')
    # p_redirect_url = f"http://127.0.0.1:8000/ccavResponseHandler/?secret_key={passwd}"
    # logger.info(p_redirect_url)
    p_cancel_url = f"{cancel_url_fetch[1:]}?secret_key={passwd}".replace('"', '')
    # logger.info(p_cancel_url)
    # p_cancel_url = f"http://127.0.0.1:8000/ccavResponseHandler/?secret_key={passwd}"
    # logger.info(p_cancel_url)
    # http://127.0.0.1:8000
    p_language = "EN"

    """
    p_billing_name = form_data['billing_name']
    p_billing_address = form_data['billing_address']
    p_billing_city = form_data['billing_city']
    p_billing_state = form_data['billing_state']
    p_billing_zip = form_data['billing_zip']
    p_billing_country = form_data['billing_country']
    p_billing_tel = form_data['billing_tel']
    p_billing_email = form_data['billing_email']
    p_delivery_name = form_data['delivery_name']
    p_delivery_address = form_data['delivery_address']
    p_delivery_city = form_data['delivery_city']
    p_delivery_state = form_data['delivery_state']
    p_delivery_zip = form_data['delivery_zip']
    p_delivery_country = form_data['delivery_country']
    p_delivery_tel = form_data['delivery_tel']
    p_merchant_param1 = form_data['merchant_param1']
    p_merchant_param2 = form_data['merchant_param2']
    p_merchant_param3 = form_data['merchant_param3']
    p_merchant_param4 = form_data['merchant_param4']
    p_merchant_param5 = form_data['merchant_param5']"""
    p_integration_type = "iframe_normal"
    # p_promo_code = form_data['promo_code']
    # p_customer_identifier = form_data['customer_identifier']

    if int(paymentview.amount) == 0:
        res, msg = rev_cltr.payment_sucess_promo(paymentview=paymentview, order_id=p_order_id, secret_key=secret_key)
        if not res:
            url = {
                "details": msg,
                "order_id": p_order_id, "user_id": paymentview.user_id
            }
            return url
        url = {
            "Bypass": True,
            "order_id": p_order_id, "user_id": paymentview.user_id
        }
        loggers['logger3'].info("/ccavRequestHandler/ : Response : " + str(dict(url)))
        loggers['logger3'].info("/ccavRequestHandler/ : status_code=200")
        return (url)
    res, msg = rev_cltr.payment_initiate(paymentview=paymentview, order_id=p_order_id, secret_key=secret_key)
    if not res:
        url = {
            "details": msg,
            "order_id": p_order_id, "user_id": paymentview.user_id
        }
        loggers['logger3'].info("/ccavRequestHandler/ : Response : " + str(dict(url)))
        loggers['logger3'].info("/ccavRequestHandler/ : status_code=200")
        return url

    # tid = p_order_id.replace("-","")
    # + '&' + 'tid=' + tid 
    merchant_data = 'merchant_id=' + p_merchant_id + '&' + 'order_id=' + p_order_id + '&' + "currency=" + p_currency + '&' + 'amount=' + p_amount + '&' + 'redirect_url=' + p_redirect_url + '&' + 'cancel_url=' + p_cancel_url + '&' + 'language=' + p_language + '&integration_type=' + p_integration_type

    # merchant_data = 'merchant_id=' + p_merchant_id + '&' + 'order_id=' + p_order_id + '&' + "currency=" + p_currency + '&' + 'amount=' + p_amount + '&' + 'redirect_url=' + p_redirect_url + '&' + 'cancel_url=' + p_cancel_url + '&' + 'language=' + p_language + '&' + 'integration_type=' + p_integration_type
    """+ '&' + 'billing_name=' + p_billing_name + '&' + 'billing_address=' + p_billing_address + '&' + 'billing_city=' + p_billing_city + '&' + 'billing_state=' + p_billing_state + '&' + 'billing_zip=' + p_billing_zip + '&' + 'billing_country=' + p_billing_country + '&' + 'billing_tel=' + p_billing_tel + '&' + 'billing_email=' + p_billing_email + '&' + 'delivery_name=' + p_delivery_name + '&' + 'delivery_address=' + p_delivery_address + '&' + 'delivery_city=' + p_delivery_city + '&' + 'delivery_state=' + p_delivery_state + '&' + 'delivery_zip=' + p_delivery_zip + '&' + 'delivery_country=' + p_delivery_country + '&' + 'delivery_tel=' + p_delivery_tel + '&' + 'merchant_param1=' + p_merchant_param1 + '&' + 'merchant_param2=' + p_merchant_param2 + '&' + 'merchant_param3=' + p_merchant_param3 + '&' + 'merchant_param4=' + p_merchant_param4 + '&' + 'merchant_param5=' + p_merchant_param5 
    + '&' + 'integration_type=' + p_integration_type + '&' + 'promo_code=' + p_promo_code + '&' + 'customer_identifier=' + p_customer_identifier + '&' """

    encryption = encrypt(merchant_data, working_key)
    logger.info(encryption)
    url = {
        "showUrl": T_url + encryption + "&access_code=" + access_code,
        "order_id": p_order_id, "user_id": paymentview.user_id
    }
    loggers['logger3'].info("/ccavRequestHandler/ : status_code=200")
    return (url)


@app.post("/ccavRequestHandler1/")  # , response_class=HTMLResponse)
async def login():
    secret_key = str(uuid.uuid4())
    p_merchant_id = merchant_id
    p_customer_name = "Mohammed"
    p_invoice_id = str(uuid.uuid4())
    logger.info("order ID !!!!!!!!!!!!!")
    # logger.info(redirect_url_fetch[1:])
    logger.info(cancel_url_fetch)
    logger.info("Handling CCavenue request")

    p_currency = "INR"
    p_invoice_amount = "1"
    passwd = encrypt_password(secret_key)
    p_redirect_url = f"{redirect_url_fetch[1:]}?secret_key={passwd}".replace('"', '')
    # p_redirect_url = f"http://127.0.0.1:8000/ccavResponseHandler/?secret_key={passwd}"
    # logger.info(p_redirect_url)
    p_cancel_url = f"{cancel_url_fetch}?secret_key={passwd}".replace('"', '')
    # logger.info(p_cancel_url)
    # p_cancel_url = f"http://127.0.0.1:8000/ccavResponseHandler/?secret_key={passwd}"
    # logger.info(p_cancel_url)
    # http://127.0.0.1:8000
    p_language = "EN"

    """
    p_billing_name = form_data['billing_name']
    p_billing_address = form_data['billing_address']
    p_billing_city = form_data['billing_city']
    p_billing_state = form_data['billing_state']
    p_billing_zip = form_data['billing_zip']
    p_billing_country = form_data['billing_country']
    p_billing_tel = form_data['billing_tel']
    p_billing_email = form_data['billing_email']
    p_delivery_name = form_data['delivery_name']
    p_delivery_address = form_data['delivery_address']
    p_delivery_city = form_data['delivery_city']
    p_delivery_state = form_data['delivery_state']
    p_delivery_zip = form_data['delivery_zip']
    p_delivery_country = form_data['delivery_country']
    p_delivery_tel = form_data['delivery_tel']
    p_merchant_param1 = form_data['merchant_param1']
    p_merchant_param2 = form_data['merchant_param2']
    p_merchant_param3 = form_data['merchant_param3']
    p_merchant_param4 = form_data['merchant_param4']
    p_merchant_param5 = form_data['merchant_param5']"""
    p_integration_type = "iframe_normal"
    # p_promo_code = form_data['promo_code']
    # p_customer_identifier = form_data['customer_identifier']

    merchant_data = 'Merchant_Id=' + p_merchant_id + '&' + 'Customer_Name=' + p_customer_name + '&' + 'Delivery_Type=SMS&Customer_Mobile_Number=8088178311&Quick_Invoice_SMS=Pls pay your amount 1.00&Invoice_Amount=INR1.00&Invoice_Valid_For=10Days&'
    # merchant_data = 'merchant_id=' + p_merchant_id + '&' + 'order_id=' + p_order_id + '&' + "currency=" + p_currency + '&' + 'amount=' + p_amount + '&' + 'redirect_url=' + p_redirect_url + '&' + 'cancel_url=' + p_cancel_url + '&' + 'language=' + p_language + '&' + 'integration_type=' + p_integration_type
    """+ '&' + 'billing_name=' + p_billing_name + '&' + 'billing_address=' + p_billing_address + '&' + 'billing_city=' + p_billing_city + '&' + 'billing_state=' + p_billing_state + '&' + 'billing_zip=' + p_billing_zip + '&' + 'billing_country=' + p_billing_country + '&' + 'billing_tel=' + p_billing_tel + '&' + 'billing_email=' + p_billing_email + '&' + 'delivery_name=' + p_delivery_name + '&' + 'delivery_address=' + p_delivery_address + '&' + 'delivery_city=' + p_delivery_city + '&' + 'delivery_state=' + p_delivery_state + '&' + 'delivery_zip=' + p_delivery_zip + '&' + 'delivery_country=' + p_delivery_country + '&' + 'delivery_tel=' + p_delivery_tel + '&' + 'merchant_param1=' + p_merchant_param1 + '&' + 'merchant_param2=' + p_merchant_param2 + '&' + 'merchant_param3=' + p_merchant_param3 + '&' + 'merchant_param4=' + p_merchant_param4 + '&' + 'merchant_param5=' + p_merchant_param5 
    + '&' + 'integration_type=' + p_integration_type + '&' + 'promo_code=' + p_promo_code + '&' + 'customer_identifier=' + p_customer_identifier + '&' """

    # customer_name|currency|valid_for|valid_type|amount|bill_delivery_type|merchant_reference_no|
    # merchant_reference_no1|merchant_reference_no2|sub_acc_id|terms_and_conditions|mobile_no|
    # sms_content|customer_email_id|customer_email_subject|invoice_description|file_name$ file_content^file_name$file_content|
    # "customer_mobile_no":8088178311,

    dic = {
        "customer_name": "MO",
        "bill_delivery_type": "EMAIL",
        "customer_mobile_no": 8088178311,
        "customer_email_id": "<EMAIL>",
        "customer_email_subject": "Test",
        "invoice_description": "Test",
        "currency": "INR",
        "valid_for": 2,
        "valid_type": "days",
        "amount": 10.0
    }

    """sms_content":"HI",
        "merchant_reference_no": 0,
        "merchant_reference_no1": 0,
        "merchant_reference_no2": 0,
        "merchant_reference_no3": 0,
        "merchant_reference_no4": 0,
        "sub_acc_id":"",
        "terms_and_conditions": "",
        "sms_content":"HI",
        "files": [{
        "name": "",
        "content": ""
            }]
            }"""

    """dic = {
        "reference_no":"28402e1b-3573-411d-9dd8-94040469e4a0",
        "refund_amount":"1.0",
        "refund_ref_no":"API1234"
        }"""

    dic2 = {"reference_no": "112958167052", "amount": "1.00"}

    dic3 = {

        "order_no": "33231644"
    }

    json_dic = json.dumps(dic)
    encryption = encrypt(json_dic, working_key)
    print(json_dic)
    merchant_data = "Mohammed|INR|30|minutes|1.00|EMAIL|<EMAIL>|test invoice mail|this invoicegenerate for testing|"
    encryption = encrypt(json_dic, working_key)
    print(encryption)
    logger.info(encryption)
    url = {

        "showUrl": 'https://secure.ccavenue.com/transaction.do?command=generateQuickInvoice&encRequest=' + encryption + '&access_code=' + access_code + '&request_type=JSON&version=1.2'
    }
    return (url)


# 0&access_code=8JXENNSSBEZCU8KQ&command=confirmOrder&request_type=XML&response_type=XML&version=1.1
# https://apitest.ccavenue.com/apis/servlet/DoWebTranstransaction.do?command=generateQuickInvoice&encRequest='
# https://secure.ccavenue.com/transaction.do?command=confirmOrder&encRequest=' + encryption + '&access_code=' + access_code+'&request_type=JSON'
# https://secure.ccavenue.com/transaction.do?command=generateQuickInvoice&encRequest=' + encryption + '&access_code=' + access_code+'&request_type=JSON&version=1.2'
#

import re


def decrypt6(cipherText, workingKey):
    iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
    encDigest = hashlib.md5(workingKey.encode('utf-8')).digest()
    dec_cipher = AES.new(encDigest, AES.MODE_CBC, iv)
    decryptedText = dec_cipher.decrypt(bytes.fromhex(cipherText))
    text = decryptedText.rstrip(b'\0').decode('utf-8')
    logger.info(text)

    logger.info(type(text))
    logger.info(len(text))
    # text1 = re.sub(r'[^\x00-\x7F]', '', text)
    text1 = text[:text.rfind('}') + 1]
    # text1=text.rstrip()
    logger.info(text1)
    logger.info(len(text1))
    return text1


def decrypt2(cipherText, workingKey):
    iv = bytes('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0a\x0b\x0c\x0d\x0e\x0f', 'utf-8')
    encDigest = hashlib.md5(workingKey.encode('utf-8')).digest()
    dec_cipher = AES.new(encDigest, AES.MODE_CBC, iv)
    decryptedText = dec_cipher.decrypt(bytes.fromhex(cipherText))
    text = decryptedText.rstrip(b'\0').decode('utf-8')
    logger.info(text)

    logger.info(type(text))
    logger.info(len(text))
    # text1 = re.sub(r'[^\x00-\x7F]', '', text)
    text1 = text.replace(chr(4), '')
    # text1=text.rstrip()
    logger.info(text1)
    logger.info(len(text1))
    return text1


def Invoice_update(merchant_json_data: dict):
    merchant_data_json = json.dumps(merchant_json_data)
    encrypted_data = encrypt(merchant_data_json, working_key)
    print(encrypted_data)
    payload = {
        'enc_request': encrypted_data,
        'access_code': access_code,
        'command': 'updateBillMerchantReferenceNo',
        'request_type': 'JSON',
        'response_type': 'JSON',
        "version": '1.1'
    }

    response = requests.post(adhoc_url, data=payload)
    if response.status_code == 200:
        logger.info(response.text)
        encrypted_response = response.text.split('&')[1].split('=')[1]
        logger.info("sucess Response update quick payment")
        logger.info(encrypted_response)
    return


@app.post("/order_adhoc")
def order_status(data: One_Time_Payment):
    if not data.user_id:
        raise HTTPException(409, "User id is missing")

    if int(data.amount) == 0:
        order_id = random.randint(10 ** 9, (10 ** 10) - 1)
        payment_gateway_data_dict = get_payment_DAO(
            order_id=order_id,
            user_id=data.user_id,
            amount=data.amount,
            appointment_id=data.appointment_id,
            promo_code=data.promo_code,
            payment_status="Successful",
            is_activity_confirmed=True,
            url=""
        )

        rev_cltr.one_time_pay_datastore(data=payment_gateway_data_dict)
        rev_cltr.confirm_appointment(id=data.appointment_id)
        url = {"Bypass": True}
        return (url), None

    res = doctor_ctrl.get_patient_details(patientid=data.user_id)
    if not res:
        raise HTTPException(409, f"user not found for userId:{data.user_id}")

    name = res['firstname'] + " " + res['lastname']
    mobile = res['mobile'].replace("+91", "")
    email = res['email']

    payload = get_adhoc_mechant_json(name, mobile, email, data.amount)
    logger.info(f"Encrypted Request:{payload}")
    response = requests.post(adhoc_url, data=payload)

    if response.status_code != 200:
        return {'status_revert': f'web transaction failure: {response}'}, {str(response.json())}

    encrypted_response = response.text.split('&')[1].split('=')[1]
    logger.info("sucess Response quick payment")
    logger.info(encrypted_response)
    status = decrypt6(encrypted_response, working_key)
    x = json.loads(status)
    if x['invoice_id'] == "":
        status = {'status_revert': 'Failed to fetch order status'}
        details = "No response from CCavenue quick book API. Booking not comfirmed"
        return status, details

    payment_gateway_data_dict = get_payment_DAO(
        order_id=x.get('invoice_id'),
        user_id=data.user_id,
        amount=data.amount,
        appointment_id=data.appointment_id,
        promo_code=data.promo_code,
        payment_status="initiate",
        is_activity_confirmed=False,
        url=x.get('tiny_url', None)
    )

    rev_cltr.one_time_pay_datastore(data=payment_gateway_data_dict)
    return {'status_revert': x['invoice_id']}, None


@app.post("/adhoc_payment")
def adhoc_payment(data: Adhoc_Payment):
    try:
        if not data.user_id:
            raise HTTPException(422, "userid is missing")

        user_details = doctor_ctrl.get_patient_details(patientid=data.user_id)
        if not user_details:
            raise HTTPException(409, f"No user found for userid: {data.user_id}")

        # TODO: Put the logic for only mobile and email
        name = user_details['firstname'] + " " + user_details['lastname']
        mobile = user_details['mobile'].replace("+91", "")
        email = user_details['email']

        payload = get_adhoc_mechant_json(name, mobile, email, data.amount)
        response = requests.post(adhoc_url, data=payload)
        if response.status_code != 200:
            raise HTTPException(409, "")

        encrypted_response = response.text.split('&')[1].split('=')[1]
        logger.info(encrypted_response)
        status = decrypt6(encrypted_response, working_key)
        x = json.loads(status)
        paymentview = Adhocview(order_id=x['invoice_id'], user_id=data.user_id, amount=data.amount,
                                appointment_id=data.appointment_id)
        rev_cltr.adhoc_initiate(paymentview=paymentview)
        return {'status_revert': x['invoice_id']}

    except Exception as e:
        raise HTTPException(409, f'Error in adhoc payment: {str(e)}')


@app.post('/inoice/receipt', tags=["Invoice"])
async def get_invoice_receipt(invoice_id: str, token: str = Depends(
    oauth2_scheme_admin)):  # , token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        return rev_cltr.get_invoice_repecipt(id=invoice_id)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/get/invoice/list', tags=["Invoice"])
async def get_payment(token: str = Depends(oauth2_scheme_admin)):  # , token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_or_doctor_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        return rev_cltr.get_invoice()

    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/mental_health/get_doctors_to_refer', tags=["Mental Health"])
async def get_doctors_list_for_referral(data: GetReferralDoctorsList, token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_doctor_list_for_referral(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/metadata/dropdown_list_names/add', tags=["Metadata"])
async def add_list_names_for_metadata(data: MetadataListName, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.add_list_names_of_metadata(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_list_names/update', tags=["Metadata"])
async def update_list_names_for_metadata(data: MetadataListNameUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.update_list_names_of_metadata(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_list_names/get', tags=["Metadata"])
async def get_list_names_for_metadata(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_list_names_of_metadata()
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_elements/add', tags=["Metadata"])
async def add_dropdown_elements(data: DropdownMetadata, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.add_dropdown_elements(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_elements/update', tags=["Metadata"])
async def update_dropdown_elements(data: DropdownMetadataUpdate, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.update_dropdown_elements(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_elements/delete', tags=["Metadata"])
async def delete_dropdown_elements(data: DropdownMetadataDelete, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.delete_dropdown_elements(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/metadata/dropdown_elements/get', tags=["Metadata"])
async def get_dropdown_elements(data: DropdownMetadataSearch, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_dropdown_elements(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/sample_csv_download', tags=["Metadata"])
async def get_sample_csv_file_for_bulk_meds_upload(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.get_sample_csv_file_for_bulk_meds_upload()
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/add_csv', tags=["Metadata"])
async def add_medicines_from_csv_file(file: UploadFile = File(...), token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.add_medicine_in_ayoo_drug_catalogue_csv_file(data=file)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/add_single_drug', tags=["Metadata"])
async def add_medicines(data: AddMedicineToCatalogue, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.add_single_medicine_in_ayoo_drug_catalogue(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/update_single_drug', tags=["Metadata"])
async def update_medicines(data: UpdateMedicineInCatalogue, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.update_single_medicine_in_ayoo_drug_catalogue(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/delete_single_drug', tags=["Metadata"])
async def delete_medicines(data: DeleteMedicineFromCatalogue, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.delete_single_medicine_in_ayoo_drug_catalogue(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/medicine_catalogue/get', tags=["Metadata"])
async def get_complete_drug_catalogue(token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_complete_drug_catalogue()
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/drug/medicine_salt', tags=["Metadata"])
async def get_medicine_salts_list(data: GetMedicineSaltFromBrand, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_medicine_salts(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/drug/medicine_brands', tags=["Metadata"])
async def get_medicine_brand_list(data: GetMedicineBrandFromSalt, token: str = Depends(oauth2_scheme_admin)):
    try:
        admin_id = get_valid_admin_or_doctor_token(token=token)
    except HTTPException as e:
        admin_id = get_user_id_from_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_medicine_brands_from_salts(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/drug/medicine_dose_and_drug_form', tags=["Metadata"])
async def get_medicine_dose_and_drug_form(data: GetMedicineDoseAndDrugFormFromBrandAndSalt,
                                          token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_or_doctor_token(token=token)
    if admin_id is not None:
        resp = metadata_ctrl.get_medicine_dose_and_drug_form(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/mental_health/patients_prescription', tags=["Mental Health"])
async def get_patients_prescription(request_data: RequestPrescriptionList, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        if request_data.patient_id and len(request_data.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(user_id, request_data.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                user_id = request_data.patient_id
        resp = psychiatry_ctrl.get_users_prescription(patient_id=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/patients_prescriptions/latest', tags=["Mental Health"])
async def get_patients_prescription(request_data: RequestPrescriptionList, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        if request_data.patient_id and len(request_data.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(user_id, request_data.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                user_id = request_data.patient_id
        resp = psychiatry_ctrl.get_users_latest_prescription(patient_id=user_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/mental_health/relatives_prescription', tags=["Mental Health"])
async def get_relatives_prescription(relative_data: RelativePrescriptions, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp = psychiatry_ctrl.get_relatives_prescription(care_taker_id=user_id, relative_id=relative_data.relative_id)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/mental_health/add_ICD', tags=["Metadata"])
async def add_icd_codes_from_csv_file(file: UploadFile = File(...), token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = metadata_ctrl.add_icd_codes_by_csv(data=file)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get('/mental_health/icd_full_list', tags=["Metadata"])
async def get_all_icd_codes(token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    if doctor_id is not None:
        resp = metadata_ctrl.get_icd_codes()
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/mental_health/icd_code_search', tags=["Metadata"])
async def search_icd_categories(search_string: SearchICDCodeByBlock, token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    if doctor_id is not None:
        resp = metadata_ctrl.search_by_block(data=search_string)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get('/user/count', tags=["dashboard"])  # api not in use -- router.py -- /user/count
async def get_user_count(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resigstered_count = ctrl.user_count()
        last_week_count = chat_ctrl.get_count()

        """resp,msg= chat_ctrl.get_ayoo_support_id ()
        for res in resp:
            adminid=res['admin_id']"""

        weekly_registrations = AllPatientsDAO().list(filters=LastSevenDaysRegistered())

        users_list = AppointmentDAO().execute_pipeline(user_appointment_aggregation)
        user_list_with_appointments = [x.get("_id") for x in users_list if x.get("count", 0) > 0]
        all_users_count = AllPatientsDAO().get_count()
        count_appointment_users = AllPatientsDAO().get_count(
            filters=UsersWithAppointments(userid=user_list_with_appointments))
        last_week_users_with_appointments = [x for x in weekly_registrations if
                                             x.get("userid") in user_list_with_appointments]
        last_week_appointment_data = AppointmentDAO().execute_pipeline(admin_dashboard_past_week_appointments_by_day)

        last_weeks_dates = []
        current_datetime = datetime.now()
        seven_days_ago = current_datetime - timedelta(days=7)
        midnight_time = seven_days_ago.replace(hour=0, minute=0, second=0, microsecond=0)
        output_format = "%Y-%m-%d"
        # Format the date and time in the required format
        formatted_date_time = midnight_time.strftime(output_format)
        date_format = "%Y-%m-%d"

        current_date = datetime.strptime(current_datetime.strftime(output_format), date_format)

        last_7_days_dates = [current_date - timedelta(days=i) for i in range(7)]
        formatted_dates = [date.strftime(date_format) for date in last_7_days_dates]

        print(formatted_dates)

        for item in last_week_appointment_data:
            item["date"] = item["date"].strftime(date_format)

        print(last_week_appointment_data)

        for date in formatted_dates:
            if date not in [x["date"] for x in last_week_appointment_data]:
                last_week_appointment_data.append({"date": date, "app_count": 0})

        last_week_appointment_data.sort(key=lambda x: x.get("date"))

        dic = {
            'resigstered_count': resigstered_count,
            'appointment': last_week_appointment_data,
            'user': last_week_count['user'],
            'user_count': last_week_count['user_count'],
            'appointment_count': last_week_count['appointment_count'],
            'users_with_appointments': count_appointment_users,
            'all_users_count': all_users_count

        }

        return dic

        # return DoctorSignupResponse(ayooid=user_id, email=resp.email, mobile=resp.mobile)
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/join_appointment', tags=["appointment"])
async def get_appointment_details_of_user(appointment_id: str = Query(...), token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(user_id)
    if user_check is None:
        raise HTTPException(status_code=409, detail='Invalid Token')

    resp = ptn_ctrl.get_appointment_details_for_user(appointment_id=appointment_id, logged_in_user=user_id)
    return resp


@app.post('/admin/create_promo', tags=["promo_code"])
async def create_promo_code(data: promo_code, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        dic = rev_cltr.create_promo(request=data)
        return dic
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get('/admin/get_all_promos', tags=["promo_code"])
async def create_promo_code(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        dic = rev_cltr.get_all_promo()
        return dic
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get('/admin/get_promo_id', tags=["promo_code"])
async def create_promo_code(id: str, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        dic = rev_cltr.get_promo_by_id(id=id)
        return dic
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.get('/admin/update_promo_id', tags=["promo_code"])
async def create_promo_code(id: str, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        dic = rev_cltr.update_promo_by_id(id=id)
        # val= {'data':dic,'msg':'promotion created'}
        return dic
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')


@app.post('/admin/promotion/apply', tags=["admin"])
async def apply_promotion(data: promotion_admin):
    data1 = promotion(promo_code=data.promo_code, amount=data.amount)
    dic = rev_cltr.apply_promotion(data=data1, id=data.userid)

    return dic


@app.post('/promotion/apply', tags=["promo_code"])
async def apply_promotion(data: promotion, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)

    dic = rev_cltr.apply_promotion(data=data, id=userid)

    return dic


@app.get('/fetch_available_coupon', tags=["promo_code"])
async def fetch_available_coupon(patient_id: str, doctor_id: str, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    dic = rev_cltr.fetch_promotion_for_user(patient_id=patient_id, doctor_id=doctor_id)
    return dic


@app.post('/app_download_link', tags=["downloads"])
async def get_app_download_link(user_data: AppDownloadLink):
    response = ptn_ctrl.get_app_download_link(user_data=user_data)
    return response


@app.post('/admin/add_new_admin', tags=["admin"])
async def create_new_admin(data: AddAdmin, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = admin_ctrl.add_admin(admin_data=data, logger_in_admin=userid)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/allowed_access', tags=["admin"])
async def get_allowed_access_for_admins(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        resp = admin_ctrl.get_admin_access(logger_in_admin=userid)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/admin/patient_relatives_list', tags=["admin"])
async def get_patient_relatives_list_from_admin(data: PatientsRelatives, token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(admin_id)
    if admin_check is not None:
        resp = ptn_ctrl.get_patient_relatives_list_from_admin_and_user(data=data)
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.post('/patient/patient_relatives_list', tags=["user"])
async def get_patient_relatives_list_from_admin(data: PatientsRelatives, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    relation = None
    if data.relation:
        relation = 'Spouse' if data.relation == 'Couple' else data.relation
    if user_id is not None:
        resp = ptn_ctrl.get_patient_relatives_list_from_admin_and_user(data=PatientsRelatives(
            patient_id=user_id,
            relation=relation
        ))
        return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/blogs/all', tags=["Blogs"], response_model=ResourceListingResponseDTO)
async def get_all_blogs():
    blogs_list = blog_ctrl.get_all_blogs()
    return blogs_list


@app.post('/blog', tags=["Blogs"], response_model=ResourceCreationResponseDTO)
async def create_resource(resource_data: ResourceNewDTO):
    added, msg = blog_ctrl.add_resource(resource_data)
    if added:
        return {"detail": msg}

    raise HTTPException(status_code=409, detail=msg)


@app.get('/blog/{blogid}', tags=["Blogs"])
async def get_blog_details(blogid: str):
    blog = blog_ctrl.get_blog_by_id(blogid)
    if blog:
        return blog
    else:
        raise HTTPException(status_code=404, detail="Blog not found")


@app.put('/blog/{resource_id}', tags=["Blogs"], response_model=ResourceCreationResponseDTO)
async def create_resource(resource_id: str, resource_data: ResourceNewDTO):
    updated, msg = blog_ctrl.update_resource(resource_id, resource_data)
    if updated:
        return {"detail": msg}

    raise HTTPException(status_code=409, detail=msg)


#
# @app.get('/payment_status_script')
# async def payment_status_script(request_body: OrderQueryRequest):
#     data = schedule_ctrl.Invoice_status(request_body, invoice_id=None, )
#     if data:
#         return data
#     else:
#         raise HTTPException(status_code=404, detail="Blog not found")


@app.post('/doctor/get_patient_details', tags=["Doctor"])
async def get_patient_details_from_doctor(case_data: GetMentalHealthCaseDetails,
                                          token: str = Depends(oauth2_scheme_doctor)):
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = psychiatry_ctrl.get_patient_complete_details(doctor_id=user_id, data=case_data)
        if not resp:
            raise HTTPException(status_code=409, detail='No data found')
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/uploadfile', tags=["fileUpload"])
async def file_upload(
        files: List[UploadFile] = File(None),
        body: FileUploadRequest = Depends(),
        token: str = Depends(oauth2_scheme)
):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    uploadedBy = userid
    user_check = ctrl.get_user_by_id(userid)
    if user_check is not None:
        if body.patient_id and len(body.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(caretakerid=userid, relativeid=body.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                userid = body.patient_id
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')

    recordId = str(body.recordId) if body.recordId else None
    if recordId is not None:
        msg, error = file_upload_ctrl.file_upload_with_record_id(userid, uploadedBy, body)

    elif files is not None:
        for file in files:
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in ALLOWED_FILE_TYPES:
                raise HTTPException(
                    status_code=HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="Unsupported file type. Allowed types: {}".format(ALLOWED_FILE_TYPES)
                )
        msg, error = file_upload_ctrl.file_upload(userid, uploadedBy, body, files)

    else:
        raise HTTPException(status_code=409, detail="please provide files or requestId/recordId")

    if msg is not None:
        return {"message": msg}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.post('/uploadRequest/userid/{userId}', tags=["fileUpload"])
async def request_file_upload(userid: str, body: List[CreateUploadRequest]):
    # print("post uploadRequest Initiated")
    requestIds, msg = file_upload_ctrl.file_upload_request(userid, body)
    if not requestIds:
        raise HTTPException(status_code=409, detail=msg)
    else:
        return {"requestIds": requestIds, "message": msg}


@app.delete('/doctor/deleteRequest', tags=["fileUpload"])
async def delete_upload_request(
        request_body: deleteUploadRequest, token: str = Depends(oauth2_scheme_doctor)
):
    doctorId = get_valid_doctor_from_token(token=token)
    if doctorId is not None:
        error = file_upload_ctrl.delete_upload_request(request_body.recordId)
        if not error:
            return {"msg": "Upload Request deleted"}
        else:
            raise HTTPException(status_code=409, detail=error)
    else:
        raise HTTPException(status_code=409, detail="Doctor not found")


@app.get('/uploadRequest', tags=["fileUpload"])
async def get_file_upload_request(
        query: GetUploadRequest = Depends(),
        token: str = Depends(oauth2_scheme)
):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(userid)
    if user_check is not None:
        if query.patient_id and len(query.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(userid, query.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                userid = query.patient_id
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')
    # print("get uploadRequest Initiated")
    requestUploads, msg = file_upload_ctrl.get_upload_request(userid, query)
    if msg is None:
        return requestUploads
    else:
        raise HTTPException(status_code=409, detail="request Failed " + str(msg))


@app.get('/uploadfile', tags=["fileUpload"])
async def get_user_file_upload(
        query: GetUserFiles = Depends(),
        token: str = Depends(oauth2_scheme)
):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(userid)
    if user_check is not None:
        if query.patient_id and len(query.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(userid, query.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                userid = query.patient_id
    else:
        raise HTTPException(status_code=409, detail='Invalid Token')
    response, msg = file_upload_ctrl.get_upload_file(userid, query)
    if msg is None:
        print(query)
        if not query.isShared:
            data = [x for x in response.get("data") if x.get('fileTag') != "71b891ee-9462-4e71-8ee0-e731854d094b"]
            response["data"] = data
            response["totalCount"] = len(data)
        else:
            response = [x for x in response if x.get('fileTag') != '71b891ee-9462-4e71-8ee0-e731854d094b']
        return response
    else:
        raise HTTPException(status_code=409, detail=msg)


@app.post('/uploadTag', tags=["fileUploadTags"])
async def create_file_upload_tag(body: CreateFileTag):
    response, error = file_tag_ctrl.file_tag_create(body)
    if not error:
        return {"msg": response}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.get('/uploadTag', tags=["fileUploadTags"])
async def get_file_upload_tag(query: GetFileTag = Depends()):
    response, error = file_tag_ctrl.file_tags_get(query)
    if not error:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)


@app.patch('/uploadTag/tagId/{tagId}', tags=["fileUploadTags"])
async def update_file_upload_tag(tagId, query: UpdateFileTag):
    response, error = file_tag_ctrl.file_tags_update(tagId, query)
    if not error:
        return {"msg", response}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.post('/relative_consent_script', tags=["admin_scripts"])
async def consent_script(token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    admin_check = admin_ctrl.get_admin_by_id(userid)
    if admin_check is not None:
        relative_ctrl.script_to_update_relatives_consent_data()
        return {
            'msg': 'Success'
        }
    else:
        raise HTTPException(status_code=409, detail='Invalid Admin')


@app.get('/doctor/uploadfile/userId/{userId}/caseId/{caseId}', tags=["fileUpload"])
async def get_all_files_for_case(
        userId, caseId, token: str = Depends(oauth2_scheme_doctor)
):
    doctorId = get_valid_doctor_from_token(token=token)
    if doctorId is not None:
        response, error = file_upload_ctrl.get_all_files_for_case(userId, caseId)
        if response is not None:
            return response
        else:
            raise HTTPException(status_code=409, detail=error)
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.get('/doctor/uploadfile/userId/{userId}/caseId/{caseId}/list', tags=["fileUpload"])
async def get_all_files_for_case_list(
        userId, caseId, token: str = Depends(oauth2_scheme_doctor)
):
    doctorId = get_valid_doctor_from_token(token=token)
    if doctorId is not None:
        response, error = file_upload_ctrl.get_all_files_for_case_list(userId, caseId)
        if response is not None:
            return response
        else:
            raise HTTPException(status_code=409, detail=error)
    else:
        raise HTTPException(status_code=409, detail='Invalid Doctor')


@app.post('/requestForms', tags=["RequestForms"])
async def create_request_form(
        file: UploadFile = File(...),
        params: CreateRequestForms = Depends(),
        token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    response, error = request_form_ctrl.request_form_create(userid, params, file)
    if not error:
        return {"msg": response}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.get('/requestForms', tags=["RequestForms"])
async def get_request_form(query: getRequestForm = Depends()):
    response, error = request_form_ctrl.request_forms_get(query)
    if not error:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)


@app.patch('/requestForms/formId/{formId}', tags=["RequestForms"])
async def update_file_request_form(formId, query: updateRequestForm, token: str = Depends(oauth2_scheme_admin)):
    userid = get_valid_admin_from_token(token=token)
    response, error = request_form_ctrl.request_forms_update(formId, query)
    if not error:
        return {"msg": response}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.post('/shareFile', tags=["fileUpload"])
async def share_file_from_locker(
        request: ShareFileReq = Depends(),
        token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    user_check = ctrl.get_user_by_id(userid)
    if user_check is not None:
        if request.patient_id and len(request.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(userid, request.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                userid = request.patient_id
        resp, error = file_upload_ctrl.share_file_from_locker(userid, request)
        if not error:
            return resp
        else:
            raise HTTPException(status_code=409, detail=error)


@app.delete('/deleteRequest', tags=["fileUpload"])
async def delete_file_request_doctor(request_body: deleteUserFile, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid:
        error = file_upload_ctrl.delete_file_record(request_body.recordId)
        if not error:
            return {"msg": "File Deleted"}
        else:
            raise HTTPException(status_code=409, detail=error)
    else:
        raise HTTPException(status_code=409, detail="User not found")


@app.get("/getActiveCases")
async def get_active_cases(data: GetActiveCases = Depends(), token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid:
        if data.patient_id and len(data.patient_id) > 5:
            resp = relative_ctrl.validate_realtive(userid, data.patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")
            else:
                userid = data.patient_id
        resp, msg = ptn_ctrl.get_all_active_cases(userid)
        if not msg:
            return resp
        else:
            raise HTTPException(status_code=409, detail=msg)
    else:
        raise HTTPException(status_code=409, detail="User not found")


@app.post('/family/add_new_member', tags=["family"])
async def add_new_family_member(data: AddFamilyMember, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        loggers['logger_manage_family'].info("/family/add_new_member : Request - {}".format(data))
        resp = ptn_ctrl.add_family_member(family_member_data=data, care_taker_id=user_id)
        loggers['logger_manage_family'].info("/family/add_new_member : Response - {}".format(resp))
        return resp
    else:
        loggers['logger_manage_family'].error("/family/add_new_member : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/family/update_relative', tags=["family"])
async def add_new_family_member(data: UpdateFamilyMember, token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        loggers['logger_manage_family'].info("/family/update_relative : Request - {}".format(data))
        resp = ptn_ctrl.update_relative(relative_data=data, care_taker_id=user_id)
        loggers['logger_manage_family'].info("/family/update_relative : Response - {}".format(resp))
        return resp
    else:
        loggers['logger_manage_family'].error("/family/update_relative : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/family/register_added_member', tags=["family"])
async def register_added_family_member(data: FamilyMemberSignUpView):
    loggers['logger_manage_family'].info("/family/register_added_member : Request - {}".format(data))
    resp, msg = ctrl.signup(signup_viewmodel=UserSignUpView(firstname=data.firstname,
                                                            lastname=data.lastname,
                                                            dob=data.dob,
                                                            email=data.email,
                                                            mobile=data.mobile,
                                                            password=data.password,
                                                            gender=data.gender), relative_id=data.relative_id)
    if not resp:
        loggers['logger_manage_family'].error("/family/register_added_member : Request - {}".format(msg))
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger_manage_family'].info("/family/register_added_member : Response - {}".format(resp))
        return SignupResponse(transaction_id=resp.transactionid, mobile=resp.mobile)


@app.post('/family/generate_caretaker_access_code', tags=['family'])
async def generate_caretaker_access_code(data: GenerateCaretakerAccessCodeView, token: str = Depends(oauth2_scheme)):
    loggers['logger_manage_family'].info("/family/generate_caretaker_access_code : Request - {}".format(data))
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if not userid:
        loggers['logger_manage_family'].info("/family/generate_caretaker_access_code : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')
    resp, msg = ptn_ctrl.generate_caretaker_access_code(userid=userid, relation_id=data.realtion_id,
                                                        consent_duration=data.consent_duration)
    if not resp:
        loggers['logger_manage_family'].error("/family/generate_caretaker_access_code : Response - {}".format(msg))
        raise HTTPException(status_code=409, detail=msg)
    else:
        loggers['logger_manage_family'].info("/family/generate_caretaker_access_code : Response - {}".format(resp))
        return resp


@app.post('/family/get_caretaker_access_by_code', tags=['family'])
async def get_caretaker_access_by_code(data: GetCaretakerAccessByCodeView, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    loggers['logger_manage_family'].info("/family/get_caretaker_access_by_code : Request - {}".format(data))
    if not userid:
        loggers['logger_manage_family'].error("/family/get_caretaker_access_by_code : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')
    verify_resp, msg = ptn_ctrl.verify_caretaker_access_code(access_code=data.access_code, userid=userid)
    if not verify_resp:
        loggers['logger_manage_family'].error("/family/get_caretaker_access_by_code : Response - {}".format(msg))
        raise HTTPException(status_code=409, detail=msg)
    else:
        consent_value = True
        update_resp, msg = relative_ctrl.update_consent(verify_resp, consent_value)
        update_consent, consent_msg = ptn_ctrl.update_consent_records(
            data=ConsentRecord(relation_id=update_resp['relation_id'],
                               consent_duration=update_resp['consent_duration'],
                               to_remove=not consent_value))
        if update_resp['consent'] is True:
            loggers['logger_manage_family'].info("/family/get_caretaker_access_by_code : Response - {}".format(msg))
            return msg
        else:
            loggers['logger_manage_family'].error("/family/get_caretaker_access_by_code : Response - {}".format(msg))
            raise HTTPException(status_code=409, detail=msg)


@app.post('/family/update_caretaker_access', tags=['family'])
async def update_caretaker_access(data: UpdateCaretakerAccess, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    loggers['logger_manage_family'].info("/family/update_caretaker_access : Request - {}".format(data))
    if not userid:
        loggers['logger_manage_family'].error("/family/update_caretaker_access : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')
    if data.to_remove:
        update_resp, msg = relative_ctrl.update_consent_self(caretaker_relation_id=data.relation_id,
                                                             consent_value=False)

    else:
        update_resp, msg = ptn_ctrl.update_consent_records(data=data)
    if update_resp:
        loggers['logger_manage_family'].info("/family/update_caretaker_access : Response - {}".format(msg))
        return msg
    else:
        loggers['logger_manage_family'].error("/family/update_caretaker_access : Response - {}".format(msg))
        raise HTTPException(status_code=409, detail=msg)


# after adding an existing user as a family member, use this api to fetch pending approval and allow or disallow family member addition
@app.post('/family/approve_request', tags=["family"])
async def approve_request_for_added_family_member(data: ApproveFamilyMemberAddition,
                                                  token: str = Depends(oauth2_scheme)):
    loggers['logger_manage_family'].info("/family/approve_request : Request - {}".format(data))
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp = ptn_ctrl.approve_request_for_added_family_member(data=data, logged_in_user_id=user_id)
        loggers['logger_manage_family'].info("/family/approve_request : Response - {}".format(resp))
        return resp
    else:
        loggers['logger_manage_family'].error("/family/approve_request : Response - Invalid User")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/family/delete_relation', tags=['family'])
async def delete_family_relation(data: DeleteFamilyRelation, token: str = Depends(oauth2_scheme)):
    loggers['logger_manage_family'].info("/family/delete_relation : Request - {}".format(data))
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        resp, msg = ptn_ctrl.delete_family_relation(data=data)
        loggers['logger_manage_family'].info("/family/delete_relation : Response - {}".format(msg))
        if resp is None:
            raise HTTPException(status_code=409, detail=msg)
        return msg
    else:
        loggers['logger_manage_family'].error("/family/delete_relation : Error - Invalid user token")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.post('/admin/family/delete_relation', tags=['family'])
async def delete_family_relation(data: DeleteFamilyRelation, token: str = Depends(oauth2_scheme_admin)):
    loggers['logger_manage_family'].info(f"/family/delete_relation : Request - {data}")
    user_id = get_valid_admin_from_token(token=token)

    if user_id is not None:
        resp, msg = ptn_ctrl.delete_family_relation(data=data)
        loggers['logger_manage_family'].info(f"/family/delete_relation : Response - {msg}")
        return msg
    else:
        loggers['logger_manage_family'].error(f"/family/delete_relation : Error - Invalid user token")
        raise HTTPException(status_code=409, detail='Invalid User')


@app.patch('/uploadRequest', tags=["fileUpload"])
async def update_upload_request(body: updateUploadRequest, token: str = Depends(oauth2_scheme_doctor)):
    doctorid = get_valid_doctor_from_token(token=token)
    if not doctorid:
        raise HTTPException(status_code=409, detail="Doctor Not Founds")
    else:
        response, error = file_upload_ctrl.update_upload_request(body)
        if not error:
            return {"msg": response}
        else:
            raise HTTPException(status_code=409, detail=error)


@app.post('/uploadRequest/supportedRequests', tags=['fileUpload'])
async def get_supported_upload_request(body: List[supportedUploadRequest]):
    response, error = file_upload_ctrl.create_supported_upload_request(body)
    if not error:
        return {"msg": response}
    else:
        raise HTTPException(status_code=409, detail=error)


@app.get('/uploadRequest/supportedRequests', tags=['fileUpload'])
async def get_supported_upload_request():
    response, error = file_upload_ctrl.get_supported_upload_request()
    if not error:
        return response
    else:
        raise HTTPException(status_code=409, detail=error)


@app.post('/medical_health/chief_complain/delete1', tags=["Medical Health"])
async def delete_one_element_from_medical_health_chief_complain(chief_complain: DeleteChiefComplain,
                                                                token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/chief_complain/delete1 : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_medical_health_chief_complain_single_entity(data=chief_complain,
                                                                                      logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/chief_complain/delete1 : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/chief_complain/delete1 : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/chief_complain/delete1 : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/chief_complain/add', tags=["Medical Health"])
async def add_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/chief_complain/add : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.add_chief_complain(data=chief_complain, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/chief_complain/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/chief_complain/add : status_code=200")

        return resp
    else:
        loggers['logger4'].info("/medical_health/chief_complain/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/chief_complain/update', tags=["Medical Health"])
async def update_latest_chief_complain(chief_complain: AddChiefComplain, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/chief_complain/update : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_latest_chief_complain(data=chief_complain,
                                                                logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/chief_complain/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/chief_complain/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/chief_complain/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/chief_complain/delete', tags=["Medical Health"])
async def delete_latest_chief_complain(chief_complain: GetMentalHealthCaseDetails,
                                       token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/chief_complain/delete : request :" + str(dict(chief_complain)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_latest_chief_complain(data=chief_complain,
                                                                logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/chief_complain/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/chief_complain/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/chief_complain/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/doctor_notes/add', tags=["Medical Health"])
async def add_doctor_notes(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/doctor_notes/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.add_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/doctor_notes/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/doctor_notes/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/doctor_notes/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/doctor_notes/update', tags=["Medical Health"])
async def update_last_doctor_notes(data: DoctorNotes, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/doctor_notes/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/doctor_notes/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/doctor_notes/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/doctor_notes/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/doctor_notes/delete', tags=["Medical Health"])
async def delete_last_doctor_notes(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/doctor_notes/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_doctor_notes(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/doctor_notes/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/doctor_notes/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/doctor_notes/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/substance_use_abuse/add', tags=["Medical Health"])
async def add_substance_use_abuse(data: SubstanceUseAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/substance_use_abuse/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/substance_use_abuse/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/substance_use_abuse/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/substance_use_abuse/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/substance_use_abuse/update', tags=["Medical Health"])
async def update_last_substance_use_abuse(data: SubstanceUseAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/substance_use_abuse/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/substance_use_abuse/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/substance_use_abuse/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/substance_use_abuse/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/substance_use_abuse/delete', tags=["Medical Health"])
async def delete_last_substance_use_abuse(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/substance_use_abuse/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_substance_use_abuse(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/substance_use_abuse/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/substance_use_abuse/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/substance_use_abuse/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/assessment/add', tags=["Medical Health"])
async def add_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/assessment/update', tags=["Medical Health"])
async def update_last_assessment(data: Assessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/assessment/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/assessment/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/assessment/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/assessment/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/assessment/delete', tags=["Medical Health"])
async def delete_last_assessment(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/assessment/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/assessment/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/assessment/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/assessment/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/lab_tests/add', tags=["Medical Health"])
async def add_lab_tests(data: LabTest, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/lab_tests/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/lab_tests/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/lab_tests/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/lab_tests/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/lab_tests/update', tags=["Medical Health"])
async def update_last_lab_tests(data: LabTest, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/lab_tests/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/lab_tests/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/lab_tests/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/lab_tests/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/lab_tests/delete', tags=["Medical Health"])
async def delete_last_lab_tests(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/lab_tests/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_lab_tests(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/lab_tests/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/lab_tests/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/lab_tests/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/medications/add', tags=["Medical Health"])
async def add_medications(data: MentalHealthMedication, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/medications/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/medications/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/medications/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/medications/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/medications/update', tags=["Medical Health"])
async def update_last_medications(data: MentalHealthMedication, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/medications/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/medications/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/medications/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/medications/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/medications/delete', tags=["Medical Health"])
async def delete_last_medications(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/medications/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_medications(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/medications/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/medications/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/medications/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/work_up_plan/add', tags=["Medical Health"])
async def add_work_up_plan(data: WorkUpPlanAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/work_up_plan/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_work_up_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/work_up_plan/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/work_up_plan/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/work_up_plan/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/work_up_plan/update', tags=["Medical Health"])
async def update_last_work_up_plan(data: WorkUpPlanAdd, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/work_up_plan/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        loggers['logger4'].info("/medical_health/work_up_plan/update : status_code=200")
        resp = medical_health_ctrl.update_work_up_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/work_up_plan/update : Response : " + str(dict(res=resp)))
        return resp
    else:
        loggers['logger4'].info("/medical_health/work_up_plan/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/work_up_plan/delete', tags=["Medical Health"])
async def delete_last_work_up_plan(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/work_up_plan/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_work_up_plan(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/work_up_plan/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/work_up_plan/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/work_up_plan/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/follow_up/add', tags=["Medical Health"])
async def add_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/follow_up/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/follow_up/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/follow_up/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/follow_up/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/follow_up/update', tags=["Medical Health"])
async def update_last_follow_up(data: FollowUp, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/follow_up/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/follow_up/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/follow_up/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/follow_up/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/follow_up/delete', tags=["Medical Health"])
async def delete_last_follow_up(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/follow_up/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_follow_up(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/follow_up/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/follow_up/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/follow_up/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/client_education/add', tags=["Medical Health"])
async def add_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/client_education/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.add_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/client_education/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/client_education/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/client_education/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/client_education/update', tags=["Medical Health"])
async def update_last_client_education(data: ClientEducation, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/client_education/update : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.update_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/client_education/update : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/client_education/update : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/client_education/update : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/client_education/delete', tags=["Medical Health"])
async def delete_last_client_education(data: CaseElement, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/client_education/delete : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.delete_client_education(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/client_education/delete : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/client_education/delete : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/client_education/delete : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/care_assessment/add', tags=["Medical Health"])
async def care_assessment_add(data: CareAssessment, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/care_assessment/add : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.add_care_assessment(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/care_assessment/add : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/care_assessment/add : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/care_assessment/add : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/care_assessment/remove_attribute', tags=["Medical Health"])
async def care_assessment_remove_attribute(data: CareAssessmentRemoveAttribute,
                                           token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/care_assessment/remove_attribute : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.remove_care_assessment_attribute(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/care_assessment/remove_attribute : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/care_assessment/remove_attribute : status_code=200")
        return resp
    else:
        loggers['logger4'].info(
            "/medical_health/care_assessment/remove_attribute : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/close_case', tags=["Medical Health"])
async def add_case_summary(data: CaseSummary, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/close_case : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.record_case_summary(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/close_case : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/close_case : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/close_case : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/prescription_submit', tags=["Medical Health"])
async def psychiatrist_prescription_submit(data: CaseElement1, token: str = Depends(oauth2_scheme_doctor)):
    loggers['logger4'].info("/medical_health/prescription_submit : request :" + str(dict(data)))
    user_id = get_valid_doctor_from_token(token=token)
    if user_id is not None:
        resp = medical_health_ctrl.medical_health_prescription_submit(data=data, logged_in_user=user_id)
        loggers['logger4'].info("/medical_health/prescription_submit : Response : " + str(dict(res=resp)))
        loggers['logger4'].info("/medical_health/prescription_submit : status_code=200")
        return resp
    else:
        loggers['logger4'].info("/medical_health/prescription_submit : {status_code=409, detail= Invalid User Token")
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/medical_health/medicine_notification_toggle', tags=["Medical Health"])
async def mh_med_notif_toggle_switch(request_data: CaseElement, token: str = Depends(oauth2_scheme)):
    userid = get_valid_user_from_token(token=token, ctrl=ctrl)
    if userid is not None:
        resp, msg = medical_health_ctrl.med_notif_toggle_switch(data=request_data)
        if not resp:
            raise HTTPException(status_code=409, detail=msg)
        else:
            return resp
    else:
        raise HTTPException(status_code=409, detail='Invalid User')


@app.get('/admin/medical_history/{patient_id}', tags=["admin"])
async def get_medical_history_of_patient_from_admin(patient_id: str, token: str = Depends(oauth2_scheme_admin)):
    get_valid_admin_from_token(token=token)
    return medical_history_ctrl.get_medical_history(patient_id=patient_id)


@app.post('/admin/medical_history/{patient_id}/{key_to_add}', tags=["admin"])
async def add_medical_history_of_patient_from_admin(patient_id: str, key_to_add: str,
                                                    request_data: PatientMedicalHistory,
                                                    token: str = Depends(oauth2_scheme_admin)):
    admin_id = get_valid_admin_from_token(token=token)
    return medical_history_ctrl.add_medical_history(patient_id=patient_id, key_to_add=key_to_add, data=request_data,
                                                    logged_in_user=admin_id, added_by_type='admin')


@app.put('/admin/medical_history/{patient_id}/{key_to_update}/{key_id}', tags=["admin"])
async def update_medical_history_of_patient_from_admin(patient_id: str, key_to_update: str, key_id: str,
                                                       request_data: PatientMedicalHistory,
                                                       token: str = Depends(oauth2_scheme_admin)):
    get_valid_admin_from_token(token=token)
    return medical_history_ctrl.update_medical_history(patient_id=patient_id, key_to_update=key_to_update,
                                                       key_id=key_id, data=request_data)


@app.get('/doctor/medical_history/{patient_id}', tags=["doctor"])
async def get_medical_history_of_patient_from_doctor(patient_id: str, token: str = Depends(oauth2_scheme_doctor)):
    get_valid_doctor_from_token(token=token)
    return medical_history_ctrl.get_medical_history(patient_id=patient_id)


@app.post('/doctor/medical_history/{patient_id}/{key_to_add}', tags=["doctor"])
async def add_medical_history_of_patient_from_doctor(patient_id: str, key_to_add: str,
                                                     request_data: PatientMedicalHistory,
                                                     token: str = Depends(oauth2_scheme_doctor)):
    doctor_id = get_valid_doctor_from_token(token=token)
    return medical_history_ctrl.add_medical_history(patient_id=patient_id, key_to_add=key_to_add, data=request_data,
                                                    logged_in_user=doctor_id, added_by_type='doctor')


@app.put('/doctor/medical_history/{patient_id}/{key_to_update}/{key_id}', tags=["doctor"])
async def update_medical_history_of_patient_from_doctor(patient_id: str, key_to_update: str, key_id: str,
                                                        request_data: PatientMedicalHistory,
                                                        token: str = Depends(oauth2_scheme_doctor)):
    get_valid_doctor_from_token(token=token)
    return medical_history_ctrl.update_medical_history(patient_id=patient_id, key_to_update=key_to_update,
                                                       key_id=key_id, data=request_data)


@app.get("/doctor/user_profile", tags=["doctor"])
async def return_me(patient_id: str, token: str = Depends(oauth2_scheme)):
    get_valid_doctor_from_token(token=token)
    user_details = ctrl.get_user_details(userid=patient_id, mongo=mongodb_conn)
    if user_details is None:
        user_details = ctrl.get_relative_details(userid=patient_id, mongo=mongodb_conn)
    if user_details is None:
        raise HTTPException(status_code=409, detail='User details not found')

    relatives = ptn_ctrl.get_patient_relatives_list_from_doctor(data=PatientsRelatives(
        patient_id=user_details['userid']
    ))
    return dict(userid=user_details['userid'],
                firstname=user_details['firstname'],
                lastname=user_details['lastname'],
                dob=str(user_details['birthdate']),
                email=user_details['email'],
                mobile=user_details['mobile'],
                gender=str(user_details['gender']),
                profile_image_url=user_details['profile_image_url'],
                marital_status=user_details['marital_status'],
                address=user_details['address'],
                city=user_details['city'],
                locality=user_details['locality'],
                pin=user_details['pin'],
                state=user_details['state'],
                country=user_details['country'],
                personal_doctor_name=user_details['personal_doctor_name'],
                personal_doctor_phone=user_details['personal_doctor_phone'],
                personal_doctor_email=user_details['personal_doctor_email'],
                emergency_contacts=user_details.get('emergency_contacts'),
                relatives=relatives
                )


@app.post("/signup_for_update")
async def sign_up_update(request_data: GuestUpdatesSignUpRequest):
    guest_controller.sign_up_for_updates(request_data.email_id)


@app.get("/user/follow_up_appointments", tags=["case sheet"])
async def user_due_follow_up(patient_id: Optional[str] = '', token: str = Depends(oauth2_scheme)):
    user_id = get_valid_user_from_token(token=token, ctrl=ctrl)
    if user_id is not None:
        relationship = None
        if patient_id and len(patient_id) > 5:
            resp = relative_ctrl.validate_realtive(user_id, patient_id)
            if not resp:
                raise HTTPException(status_code=409, detail="Selected Relative not found")

            relation_data = relative_ctrl.get_relation_data_by_caretaker_and_relative(user_id, patient_id)
            consent = relation_data.consent
            if consent is not True:
                raise HTTPException(status_code=409, detail="Consent not allowed for the relative")
            user_id = patient_id
            relationship = str(relation_data.relationship)

        from ayoo_backend.api.DAOs.caseSheetDAO import CaseSheetDAO
        return CaseSheetDAO().fetch_follow_up_appointments(patient_id=user_id, relationship=relationship)
    else:
        raise HTTPException(status_code=409, detail='Invalid User Token')


@app.post('/admin/broadcast_push_notifs', tags=["admin"])
async def broadcast_push_notifs(data: BroadCastMsg, token: str = Depends(oauth2_scheme_admin)):
    get_valid_admin_from_token(token=token)
    return firebase_ctrl.broadcast_msg(msg_data=data)


@app.get('/admin/get_fcm_access_token', tags=["admin"])
async def get_fcm_access_token(token: str = Depends(oauth2_scheme_admin)):
    get_valid_admin_from_token(token=token)
    return firebase_ctrl.get_fcm_access_token()


@app.post('/admin/merge_patient_profiles', tags=["admin"])
async def merge_patient_profiles(patient_data: PatientProfileMerge, token: str = Depends(oauth2_scheme_admin)):
    get_valid_admin_from_token(token=token)
    return ptn_ctrl.merge_patient_profiles(patient_data=patient_data)
